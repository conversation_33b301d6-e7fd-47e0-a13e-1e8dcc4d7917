import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Button } from '@/components/ui/button'
import { CarbonVideoPlayer } from '@/components/icons/carbon'
import { Scissors } from 'lucide-react'
import React, { useState } from 'react'
import { create } from 'zustand'
import { createJSONStorage, persist } from 'zustand/middleware'
import VideoCutDialog from '@/pages/AIToolbox/components/VideoCutDialog'

const instructions = [
  '声音：1.用真实麦克风传输声音，或者购买声卡硬件传输声音。2.加点实时环境音。3.使用未被互联网标记的声音，多上传几个声色。4.开启声音模型自动微调。',
  '画面：1.用手机拍摄4k屏。2.用采集卡传输画面',
  '环境：电脑开播必须用纯净电脑或者虚拟机，防止直播伴侣检测进程。',
  '互动：1.每场话术清空使用后必须重新泛化，回复内容也要重新泛化；总之能泛化的地方都清空重新泛化。2.开启自动报时、主动评论、自动回复。2.开启小号制造互动',
]

interface InstructionsStore {
  checkedItems: Record<number, boolean>
  toggleItem: (index: number) => void
}

const useInstructionsStore = create<InstructionsStore>()(
  persist(
    (set) => ({
      checkedItems: {},
      toggleItem: (index: number) => {
        set((state) => ({
          checkedItems: {
            ...state.checkedItems,
            [index]: !state.checkedItems[index]
          }
        }))
      }
    }),
    {
      name: 'instructions-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
)

const AIToolboxSection = React.memo(() => {
  const [isVideoCutDialogOpen, setIsVideoCutDialogOpen] = useState(false)

  return (
    <Card>
      <CardHeader>
        <CardTitle>AI工具箱</CardTitle>
        <CardDescription>集成各种AI辅助工具，提升工作效率</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* 视频切割工具 */}
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardHeader className="pb-3">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <CarbonVideoPlayer className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <CardTitle className="text-base">视频切割工具</CardTitle>
                  <CardDescription className="text-xs">
                    支持多种切割方式，快速分割视频文件
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-3">
                <div className="text-xs text-muted-foreground">
                  • 支持拖拽上传视频文件<br />
                  • 按时间段切割<br />
                  • 按分钟自动切割<br />
                  • 高质量输出
                </div>
                <Button
                  onClick={() => setIsVideoCutDialogOpen(true)}
                  className="w-full"
                  size="sm"
                >
                  <Scissors className="w-4 h-4 mr-2" />
                  开始切割
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 预留其他工具位置 */}
          <Card className="hover:shadow-md transition-shadow opacity-50">
            <CardHeader className="pb-3">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-muted rounded-lg">
                  <div className="w-5 h-5 bg-muted-foreground/20 rounded" />
                </div>
                <div>
                  <CardTitle className="text-base">更多工具</CardTitle>
                  <CardDescription className="text-xs">
                    敬请期待...
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="text-xs text-muted-foreground">
                更多AI工具正在开发中
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 视频切割弹窗 */}
        <VideoCutDialog
          open={isVideoCutDialogOpen}
          onOpenChange={setIsVideoCutDialogOpen}
        />
      </CardContent>
    </Card>
  )
})

const InstructionsCard = React.memo(() => {
  const { checkedItems, toggleItem } = useInstructionsStore()

  return (
    <Card>
      <CardHeader>
        <CardTitle>注意事项</CardTitle>
        <CardDescription>自动回复功能目前仅对抖音小店和巨量百应开放。</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {instructions.map((instruction, index) => (
            // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
            <div className="flex gap-3 items-start" key={index}>
              <Checkbox
                checked={checkedItems[index] || false}
                onCheckedChange={() => toggleItem(index)}
                className="mt-1"
              />
              <p className="text-sm text-muted-foreground leading-6 flex-1">
                {instruction}
              </p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
})

export default InstructionsCard
export { AIToolboxSection }
