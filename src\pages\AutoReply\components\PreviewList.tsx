import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { type MessageOf, useAutoReply } from '@/hooks/useAutoReply'
import { useForbiddenWords } from '@/hooks/useForbiddenWords'
import { useAutoVoice, determineSpeaker, determineOutputType } from '@/hooks/useAutoVoice'
import { SendHorizontalIcon, CheckCircle, XCircle, Clock, AlertCircle, Loader2, Trash2, Volume2 } from 'lucide-react'
import { getForbiddenWordsInText } from '@/utils/filter'
import { speakText } from '@/utils/textToSpeech'
import { useToast } from '@/hooks/useToast'
import { useState, useRef } from 'react'
import { useVirtualizer } from '@tanstack/react-virtual'
import { IPC_CHANNELS } from 'shared/ipcChannels'

export default function PreviewList({
  setHighLight,
}: { setHighLight: (commentId: string | null) => void }) {
  const { replies, comments, updateReplyStatus, clearReplies, config } = useAutoReply()
  const { forbiddenWords } = useForbiddenWords()
  const { addVoiceItemByPriority, updateVoiceItem, removeVoiceItem } = useAutoVoice()
  const { toast } = useToast()
  const [generatingVoices, setGeneratingVoices] = useState<Set<string>>(new Set())

  // 虚拟列表相关
  const parentRef = useRef<HTMLDivElement>(null)

  // 创建虚拟列表实例
  const virtualizer = useVirtualizer({
    count: replies.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 80, // 估算每个回复项的高度
    overscan: 5, // 预渲染额外的项目数量
    // 启用动态高度测量
    measureElement: (element) => {
      return element?.getBoundingClientRect().height ?? 80
    },
  })

  const handleSendReply = async (replyContent: string, replyId: string, nickname?: string) => {
    try {
      updateReplyStatus(replyId, 'sending')

      // 查找相关评论以获取用户昵称
      const reply = replies.find(r => r.id === replyId)
      const relatedComment = reply ? comments.find(c => c.msg_id === reply.commentId) : null
      const userNickname = nickname || relatedComment?.nick_name || ''

      // 发送时需要添加@用户名前缀
      const messageToSend = userNickname ? `@${userNickname} ${replyContent}` : replyContent

      await window.ipcRenderer.invoke(
        IPC_CHANNELS.tasks.autoReply.sendReply,
        messageToSend,
      )
      updateReplyStatus(replyId, 'sent')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      console.error('发送回复失败:', errorMessage)
      updateReplyStatus(replyId, 'failed', errorMessage)
    }
  }

  const handleSendToVoice = async (replyContent: string, replyId: string) => {
    try {
      // 生成唯一ID用于跟踪这个语音项
      const voiceId = Date.now().toString() + Math.random().toString(36).substring(2, 11)

      // 设置生成状态
      setGeneratingVoices(prev => new Set(prev).add(replyId))

      // 立即创建语音项并添加到列表顶部（优先播放）
      const source = 'auto-reply'
      const speaker = determineSpeaker(source, config.liveMode, config.assistantEnabled, config.assistantMode)
      const outputType = determineOutputType(speaker, config.liveMode, config.assistantMode)
      const newVoice = {
        id: voiceId,
        text: replyContent,
        audioUrl: '', // 先设为空，生成完成后更新
        videoUrl: outputType === 'digital-human' ? '' : undefined,
        duration: 0,
        digitalHumanFile: outputType === 'digital-human' ? '' : undefined,
        source,
        speaker,
        outputType
      }

      // 根据优先级添加到列表
      addVoiceItemByPriority(newVoice)

      console.log('正在将回复内容转换为语音:', replyContent)
      const { audioUrl, duration, voiceFile } = await speakText(replyContent, undefined, speaker)

      if (audioUrl) {
        // 更新语音项的音频信息
        updateVoiceItem(voiceId, { audioUrl, duration, voiceFile })
        toast.success('语音生成完成')
        console.log('回复语音生成成功:', replyContent)
      } else {
        // 生成失败，移除语音项
        removeVoiceItem(voiceId)
        toast.error('语音生成失败，未返回音频URL')
        console.error('回复文本转语音成功但未返回音频URL')
      }
    } catch (error) {
      console.error('回复语音生成失败:', error)
      toast.error('语音生成失败，请检查语音服务器')
    } finally {
      // 清除生成状态
      setGeneratingVoices(prev => {
        const newSet = new Set(prev)
        newSet.delete(replyId)
        return newSet
      })
    }
  }

  const handleClearReplies = () => {
    clearReplies()
  }

  // 渲染回复项
  const renderReplyItem = (index: number) => {
    const reply = replies[index];
    const relatedComment = comments.find(
      c => c.msg_id === reply.commentId,
    ) as MessageOf<'comment'>

    return (
      <div
        key={reply.commentId}
        className="group py-1.5 text-sm hover:bg-muted/50 rounded-lg transition-colors mb-1"
        onMouseEnter={() => setHighLight(reply.commentId)}
        onMouseLeave={() => setHighLight(null)}
      >
        <div className="flex flex-col gap-1">
          {relatedComment && (
            <div className="text-xs text-muted-foreground">
              回复：
              {relatedComment.nick_name} -{' '}
              {relatedComment.content}
            </div>
          )}
          <div className="flex items-center justify-between gap-2">
            <div className="flex items-center gap-1">
              {/* 回复类型标签 */}
              <Badge
                variant={reply.type === 'ai' ? 'secondary' : reply.type === 'keyword' ? 'secondary' : 'outline'}
                className="text-xs"
              >
                {reply.type === 'ai' ? 'AI回复' : reply.type === 'keyword' ? '关键词' : '手动'}
              </Badge>

              {/* 状态图标 */}
              {(() => {
                switch (reply.status) {
                  case 'pending':
                    return <div title="等待发送"><Clock className="h-4 w-4 text-muted-foreground" /></div>
                  case 'sending':
                    return <div title="发送中"><Loader2 className="h-4 w-4 text-blue-500 animate-spin" /></div>
                  case 'sent':
                    return <div title="发送成功"><CheckCircle className="h-4 w-4 text-green-500" /></div>
                  case 'failed':
                    return <div title={`发送失败: ${reply.error || '未知错误'}`}><AlertCircle className="h-4 w-4 text-red-500" /></div>
                  default:
                    return null
                }
              })()}

              {/* 违禁词检查图标 */}
              {(() => {
                const forbiddenWordsInText = getForbiddenWordsInText(reply.replyContent, forbiddenWords)
                const hasForbiddenWords = forbiddenWordsInText.length > 0
                return (
                  <div title={hasForbiddenWords ? `包含违禁词：${forbiddenWordsInText.join('、')}` : "内容安全"}>
                    {hasForbiddenWords ? (
                      <XCircle className="h-4 w-4 text-red-500" />
                    ) : (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    )}
                  </div>
                )
              })()}
            </div>

            <span className="text-foreground flex-1">
              {reply.replyContent}
            </span>

            <div className="flex items-center gap-1">
              {/* 发送到语音输出列表按钮 */}
              <Button
                variant="ghost"
                size="icon"
                className="invisible group-hover:visible h-6 w-6"
                title="发送到语音输出列表"
                disabled={generatingVoices.has(reply.id)}
                onClick={() =>
                  handleSendToVoice(
                    reply.replyContent,
                    reply.id,
                  )
                }
              >
                {generatingVoices.has(reply.id) ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Volume2 className="h-4 w-4" />
                )}
              </Button>

              {/* 发送按钮 - 只在未自动发送或发送失败时显示 */}
              {(!reply.autoSent || reply.status === 'failed') && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="invisible group-hover:visible h-6 w-6"
                  title="发送到直播评论区"
                  disabled={reply.status === 'sending'}
                  onClick={() =>
                    handleSendReply(
                      reply.replyContent,
                      reply.id,
                    )
                  }
                >
                  <SendHorizontalIcon className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div>
              <CardTitle>评论回复</CardTitle>
              <CardDescription>对所有评论进行文字回复</CardDescription>
            </div>
            {replies.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearReplies}
                className="flex items-center gap-1"
                title="清空回复列表"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        </CardHeader>
        <Separator />
        <CardContent>
          {replies.length === 0 ? (
            <div className="text-center text-muted-foreground py-8 h-[calc(100vh-370px)] flex items-center justify-center">
              暂无回复数据
            </div>
          ) : (
            <div
              ref={parentRef}
              className="h-[calc(100vh-370px)] overflow-auto py-2"
            >
              <div
                style={{
                  height: `${virtualizer.getTotalSize()}px`,
                  width: '100%',
                  position: 'relative',
                }}
              >
                {virtualizer.getVirtualItems().map((virtualItem) => (
                  <div
                    key={virtualItem.key}
                    data-index={virtualItem.index}
                    ref={virtualizer.measureElement}
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      transform: `translateY(${virtualItem.start}px)`,
                    }}
                    className="px-1" // 添加间距
                  >
                    {renderReplyItem(virtualItem.index)}
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </>
  )
}
