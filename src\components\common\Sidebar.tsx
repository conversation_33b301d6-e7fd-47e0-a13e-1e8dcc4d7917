import { useAutoReply } from '@/hooks/useAutoReply'
import { useCurrentLiveControl } from '@/hooks/useLiveControl'
import { useAutoVoice } from '@/hooks/useAutoVoice'
import { cn } from '@/lib/utils'
import { NavLink } from 'react-router'
import {
  CarbonContentDeliveryNetwork,
  CarbonChat,
} from '../icons/carbon'
import { Mic, Monitor, Settings } from 'lucide-react'

interface SidebarTab {
  id: string
  name: string
  isRunning?: boolean
  icon: React.ReactNode
  platform?: LiveControlPlatform[]
}

export default function Sidebar() {
  const { isRunning: isAutoReplyRunning } = useAutoReply()
  const { isRunning: isAutoVoiceRunning } = useAutoVoice()
  const platform = useCurrentLiveControl(context => context.platform)

  const tabs: SidebarTab[] = [
    {
      id: '/',
      name: '连接',
      icon: <CarbonContentDeliveryNetwork className="w-5 h-5" />,
    },
    {
      id: '/auto-voice',
      name: '话术',
      isRunning: isAutoVoiceRunning,
      icon: <Mic className="w-5 h-5" />,
    },
    {
      id: '/auto-reply',
      name: '监控',
      isRunning: isAutoReplyRunning,
      icon: <Monitor className="w-5 h-5" />,
      platform: ['douyin', 'buyin', 'wxchannel'],
    },
    // {
    //   id: '/live-control-settings?tab=digital-human',
    //   name: '数字人形象',
    //   icon: <User className="w-5 h-5" />,
    // },
    {
      id: '/live-control-settings',
      name: '互动',
      icon: <CarbonChat className="w-5 h-5" />,
      platform: ['douyin', 'buyin', 'wxchannel'],
    },

    {
      id: '/settings',
      name: '设置',
      icon: <Settings className="w-5 h-5" />,
    },

  ]

  const filteredTabs = tabs.filter(tab => {
    if (tab.platform) {
      return tab.platform.includes(platform)
    }
    return true
  })

  return (
    <aside className="w-48 min-w-[192px] bg-background border-r">
      <div className="p-4">
        <h2 className="text-base font-semibold mb-4">功能列表</h2>
        <nav className="space-y-2">
          {filteredTabs.map(tab => (
            <NavLink
              key={tab.id}
              to={tab.id}
              className={({ isActive }) =>
                cn(
                  'flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-lg transition-all relative',
                  isActive
                    ? 'bg-primary/10 text-primary shadow-xs'
                    : 'text-muted-foreground hover:bg-muted hover:text-foreground',
                )
              }
            >
              {tab.icon}
              {tab.name}
              {tab.isRunning && (
                <span className="absolute right-3 w-2 h-2 rounded-full bg-emerald-500 animate-pulse" />
              )}
            </NavLink>
          ))}
        </nav>
      </div>
    </aside>
  )
}
