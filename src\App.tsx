import LogDisplayer from '@/components/common/LogDisplayer'
import Sidebar from '@/components/common/Sidebar'
import { ThemeProvider } from '@/components/providers/ThemeProvider'
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from '@/components/ui/context-menu'
import { Toaster } from '@/components/ui/toaster'
import { useDevMode } from '@/hooks/useDevMode'
import { RefreshCwIcon, TerminalIcon } from 'lucide-react'
import { Outlet, useNavigate } from 'react-router'
import { IPC_CHANNELS } from 'shared/ipcChannels'

import { useIpcListener } from './hooks/useIpc'
import './App.css'
import { useEffect, useState, useCallback, useRef } from 'react'
import { HtmlRenderer } from './components/common/HtmlRenderer'
import { Button } from './components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from './components/ui/dialog'
import { ScrollArea } from './components/ui/scroll-area'
import { useAccounts } from './hooks/useAccounts'
import { useAutoMessageStore } from './hooks/useAutoMessage'
import { useAutoPopUpStore } from './hooks/useAutoPopUp'
import { useAutoTimeAnnouncementStore } from './hooks/useAutoTimeAnnouncement'
import { useAutoVoice, determineSpeaker, determineOutputType } from './hooks/useAutoVoice'
import { speakText } from './utils/textToSpeech'
import { generateDigitalHumanVideo } from './utils/textToVideo'
import { useAutoReply, useAutoReplyStore } from './hooks/useAutoReply'
import { useChromeConfigStore } from './hooks/useChromeConfig'
import { useLiveControlStore } from './hooks/useLiveControl'
import { useToast } from './hooks/useToast'
import { useUpdateStore } from './hooks/useUpdate'
import { cn } from '@/lib/utils'

function useGlobalIpcListener() {
  const { handleComment } = useAutoReply()
  const { setIsConnected } = useLiveControlStore()
  const { setIsRunning: setIsRunningAutoMessage } = useAutoMessageStore()
  const { setIsRunning: setIsRunningAutoPopUp } = useAutoPopUpStore()
  const { setIsRunning: setIsRunningAutoTimeAnnouncement } = useAutoTimeAnnouncementStore()
  const { addVoiceItemByPriority, updateVoiceItem, removeVoiceItem } = useAutoVoice()
  const { setStorageState } = useChromeConfigStore()
  const { toast } = useToast()
  const { currentAccountId } = useAccounts()

  // 获取最新配置的函数
  const getCurrentConfig = () => {
    const store = useAutoReplyStore.getState()
    const context = store.contexts[currentAccountId]
    return context?.config || {
      liveMode: 'voice',
      assistantEnabled: false,
      assistantMode: 'voice'
    }
  }

  // 通用的添加语音项并生成语音/数字人的函数
  const addVoiceItemAndGenerate = async (voiceItem: any, text: string) => {
    try {
      console.log('🚀 开始执行 addVoiceItemAndGenerate:', { id: voiceItem.id, text, outputType: voiceItem.outputType })

      // 根据优先级添加到语音列表
      addVoiceItemByPriority(voiceItem)
      console.log('✅ 已根据优先级添加语音项到列表:', text)

      // 生成语音
      console.log('🎤 正在生成语音:', text, '说话者:', voiceItem.speaker)
      const { audioUrl, duration, voiceFile } = await speakText(text, undefined, voiceItem.speaker)

      if (audioUrl) {
        // 更新语音项的音频信息
        updateVoiceItem(voiceItem.id, { audioUrl, duration, voiceFile })
        console.log('语音生成成功:', text)

        // 如果输出类型是数字人，继续生成视频
        if (voiceItem.outputType === 'digital-human') {
          try {
            console.log('🎬 正在生成数字人视频:', text, '说话者:', voiceItem.speaker)
            console.log('🎬 输出类型:', voiceItem.outputType, '连续播放: 启用')

            // 导入连续数字人视频生成函数
            const { generateContinuousDigitalHumanVideo } = await import('@/utils/continuousDigitalHuman')

            // 使用连续播放功能生成数字人视频
            const videoResult = await generateContinuousDigitalHumanVideo(
              text,
              audioUrl,
              {
                enableContinuous: true,
                speaker: voiceItem.speaker,  // 这里传递了speaker
                audioDuration: duration ? duration / 1000 : undefined,
                digitalHumanFile: undefined
              }
            )

            updateVoiceItem(voiceItem.id, {
              videoUrl: videoResult.videoUrl,
              digitalHumanFile: videoResult.digitalHumanFile
            })
            console.log('🎬 数字人视频生成成功:', text)

            // 使用简化的播放服务直接播放视频
            try {
              const { simpleDigitalHumanPlaybackService } = await import('@/services/SimpleDigitalHumanPlaybackService')
              await simpleDigitalHumanPlaybackService.playVideo({
                id: voiceItem.id,
                videoUrl: videoResult.videoUrl,
                text: text,
                speaker: voiceItem.speaker
              })
              console.log('🎬 数字人视频播放完成:', text)
            } catch (playError) {
              console.error('数字人视频播放失败:', playError)
              // 播放失败不影响视频生成结果
            }
          } catch (videoError) {
            console.error('数字人视频生成失败:', videoError)
            // 视频生成失败但语音成功，保留语音
          }
        }
      } else {
        // 生成失败，移除语音项
        removeVoiceItem(voiceItem.id)
        console.error('语音生成失败，未返回音频URL')
        toast.error('语音生成失败')
      }
    } catch (error) {
      // 生成失败，移除语音项
      removeVoiceItem(voiceItem.id)
      console.error('语音生成失败:', error)
      toast.error('语音生成失败，请检查语音服务器')
    }
  }

  useIpcListener(
    IPC_CHANNELS.tasks.autoReply.showComment,
    ({ comment, accountId }) => {
      handleComment(comment, accountId)
    },
  )

  useIpcListener(IPC_CHANNELS.tasks.liveControl.disconnectedEvent, id => {
    setIsConnected(id, 'disconnected')
    toast.error('直播控制台已断开连接')
  })

  useIpcListener(IPC_CHANNELS.tasks.autoMessage.stoppedEvent, id => {
    setIsRunningAutoMessage(id, false)
    toast.error('自动发言已停止')
  })

  useIpcListener(IPC_CHANNELS.tasks.autoPopUp.stoppedEvent, id => {
    setIsRunningAutoPopUp(id, false)
    toast.error('自动弹窗已停止')
  })

  useIpcListener(IPC_CHANNELS.tasks.autoTimeAnnouncement.stoppedEvent, id => {
    setIsRunningAutoTimeAnnouncement(id, false)
    toast.error('自动报时已停止')
  })

  // 监听自动报时添加语音事件
  useIpcListener(IPC_CHANNELS.autoVoice.addTimeAnnouncement, async (data: { id: string, text: string, type: string, timestamp: number }) => {
    try {
      console.log('🔔 收到自动报时消息:', data)

      // 先添加到语音列表顶部（优先播放，显示正在生成状态）
      const source = 'time-announcement'
      const config = getCurrentConfig()
      const speaker = determineSpeaker(source, config.liveMode, config.assistantEnabled, config.assistantMode)
      const outputType = determineOutputType(speaker, config.liveMode, config.assistantMode)

      console.log('自动报时语音分配:', {
        source,
        liveMode: config.liveMode,
        assistantEnabled: config.assistantEnabled,
        assistantMode: config.assistantMode,
        determinedSpeaker: speaker,
        outputType
      })

      const voiceItem: any = {
        id: data.id,
        text: data.text,
        audioUrl: '',
        videoUrl: outputType === 'digital-human' ? '' : undefined,
        duration: 0,
        digitalHumanFile: outputType === 'digital-human' ? '' : undefined,
        outputType,
        source,
        speaker
      }

      console.log('🔔 准备调用 addVoiceItemAndGenerate，voiceItem:', voiceItem)

      // 使用新的合并函数添加并生成语音
      await addVoiceItemAndGenerate(voiceItem, data.text)

      console.log('🔔 自动报时处理完成')
    } catch (error) {
      console.error('🔔 自动报时处理失败:', error)
    }
  })

  // 监听被动互动添加语音事件
  useIpcListener(IPC_CHANNELS.autoVoice.addPassiveInteraction, async (data: { id: string, text: string, type: string, messageType: string, timestamp: number }) => {
    console.log('🎤 收到被动互动语音消息:', data)

    // 添加LogDisplayer日志
    if (window.ipcRenderer) {
      window.ipcRenderer.invoke(IPC_CHANNELS.sendLog, {
        scope: '被动互动',
        level: 'info',
        message: `App.tsx收到被动互动语音消息: ${data.text} (类型: ${data.messageType})`
      })
    }

    // 先添加到语音列表顶部（优先播放，显示正在生成状态）
    const source = 'passive-interaction'
    const config = getCurrentConfig()
    const speaker = determineSpeaker(source, config.liveMode, config.assistantEnabled, config.assistantMode)
    const outputType = determineOutputType(speaker, config.liveMode, config.assistantMode)

    console.log('被动互动语音分配:', {
      source,
      liveMode: config.liveMode,
      assistantEnabled: config.assistantEnabled,
      assistantMode: config.assistantMode,
      determinedSpeaker: speaker,
      outputType
    })

    const voiceItem: any = {
      id: data.id,
      text: data.text,
      audioUrl: '',
      videoUrl: outputType === 'digital-human' ? '' : undefined,
      duration: 0,
      digitalHumanFile: outputType === 'digital-human' ? '' : undefined,
      outputType,
      source,
      speaker
    }

    if (window.ipcRenderer) {
      window.ipcRenderer.invoke(IPC_CHANNELS.sendLog, {
        scope: '被动互动',
        level: 'success',
        message: `已添加被动互动消息到语音列表: ${data.text}`
      })
    }

    // 使用新的合并函数添加并生成语音
    await addVoiceItemAndGenerate(voiceItem, data.text)
  })

  // 监听评论回复添加语音事件
  useIpcListener(IPC_CHANNELS.autoVoice.addCommentReply, async (data: { id: string, text: string, type: string, replyType: string, timestamp: number }) => {
    console.log('💬 收到评论回复语音消息:', data)

    // 添加LogDisplayer日志
    if (window.ipcRenderer) {
      window.ipcRenderer.invoke(IPC_CHANNELS.sendLog, {
        scope: '评论回复',
        level: 'info',
        message: `App.tsx收到评论回复语音消息: ${data.text} (类型: ${data.replyType})`
      })
    }

    // 先添加到语音列表顶部（优先播放，显示正在生成状态）
    const source = 'auto-reply'
    const config = getCurrentConfig()
    const speaker = determineSpeaker(source, config.liveMode, config.assistantEnabled, config.assistantMode)
    const outputType = determineOutputType(speaker, config.liveMode, config.assistantMode)

    console.log('评论回复语音分配:', {
      source,
      liveMode: config.liveMode,
      assistantEnabled: config.assistantEnabled,
      assistantMode: config.assistantMode,
      determinedSpeaker: speaker,
      outputType
    })

    const voiceItem: any = {
      id: data.id,
      text: data.text,
      audioUrl: '',
      videoUrl: outputType === 'digital-human' ? '' : undefined,
      duration: 0,
      digitalHumanFile: outputType === 'digital-human' ? '' : undefined,
      outputType,
      source,
      speaker
    }

    if (window.ipcRenderer) {
      window.ipcRenderer.invoke(IPC_CHANNELS.sendLog, {
        scope: '评论回复',
        level: 'success',
        message: `已添加评论回复消息到语音列表: ${data.text}`
      })
    }

    // 使用新的合并函数添加并生成语音
    await addVoiceItemAndGenerate(voiceItem, data.text)
  })

  useIpcListener(IPC_CHANNELS.chrome.saveState, (id, state) => {
    setStorageState(id, state)
  })
}



function UpdateInfo() {
  const [isUpdateAlertShow, setIsUpdateAlertShow] = useState(false)
  const [updateInfo, setUpdateInfo] = useState<{
    currentVersion: string
    latestVersion: string
    releaseNote?: string
  } | null>(null)
  const updateStore = useUpdateStore()

  const navigate = useNavigate()

  useIpcListener(IPC_CHANNELS.app.notifyUpdate, info => {
    if (updateStore.enableAutoCheckUpdate) {
      setIsUpdateAlertShow(true)
      setUpdateInfo(info)
    }
  })

  const handleUpdateNow = () => {
    setIsUpdateAlertShow(false)
    navigate('/settings#update-section')
  }

  return (
    <Dialog open={isUpdateAlertShow}>
      <DialogContent>
        <DialogTitle>有新版本可用</DialogTitle>
        <DialogDescription>现在更新以体验最新功能。</DialogDescription>

        <div className="flex justify-end space-x-1 items-center text-sm text-muted-foreground">
          <span className="text-muted-foreground">v{updateInfo?.currentVersion}</span>
          <span>{'→'}</span>
          <span className="text-foreground font-bold">
            v{updateInfo?.latestVersion}
          </span>
        </div>
        {updateInfo?.releaseNote && (
          <ScrollArea className="h-64">
            <HtmlRenderer
              className="markdown-body"
              html={updateInfo?.releaseNote}
            />{' '}
          </ScrollArea>
        )}
        <div className="flex justify-end gap-2 mt-4">
          <Button variant="outline" onClick={() => setIsUpdateAlertShow(false)}>
            关闭
          </Button>
          <Button variant="default" onClick={handleUpdateNow}>
            前往更新
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

function App() {
  const { enabled: devMode } = useDevMode()
  const { accounts, currentAccountId } = useAccounts()
  const [isLogDisplayerExpanded, setIsLogDisplayerExpanded] = useState(() => {
    const storedState = localStorage.getItem('isLogDisplayerExpanded');
    return storedState ? JSON.parse(storedState) : true;
  });

  // 日志面板高度状态
  const [logDisplayerHeight, setLogDisplayerHeight] = useState(() => {
    const storedHeight = localStorage.getItem('logDisplayerHeight');
    return storedHeight ? parseInt(storedHeight, 10) : 180;
  });

  // 拖动相关状态
  const [isDragging, setIsDragging] = useState(false);
  const dragStartY = useRef(0);
  const dragStartHeight = useRef(0);

  useEffect(() => {
    const account = accounts.find(acc => acc.id === currentAccountId)
    if (account && window.ipcRenderer) {
      window.ipcRenderer.invoke(IPC_CHANNELS.account.switch, { account })
    }
  }, [accounts, currentAccountId])

  useEffect(() => {
    localStorage.setItem('isLogDisplayerExpanded', JSON.stringify(isLogDisplayerExpanded));
  }, [isLogDisplayerExpanded]);

  // 保存日志面板高度到localStorage
  useEffect(() => {
    localStorage.setItem('logDisplayerHeight', logDisplayerHeight.toString());
  }, [logDisplayerHeight]);

  // 拖动处理函数
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
    dragStartY.current = e.clientY;
    dragStartHeight.current = logDisplayerHeight;
  }, [logDisplayerHeight]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging) return;

    const deltaY = dragStartY.current - e.clientY; // 向上拖动为正值
    const newHeight = Math.max(80, Math.min(window.innerHeight * 0.6, dragStartHeight.current + deltaY)); // 限制高度范围：最小80px，最大屏幕高度的60%
    setLogDisplayerHeight(newHeight);
  }, [isDragging]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // 添加全局鼠标事件监听
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'ns-resize';
      document.body.style.userSelect = 'none';

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  useGlobalIpcListener()

  const handleRefresh = () => {
    window.location.reload()
  }

  const handleToggleDevTools = async () => {
    if (window.ipcRenderer) {
      await window.ipcRenderer.invoke(IPC_CHANNELS.chrome.toggleDevTools)
    }
  }

  return (
    <ThemeProvider>
      <ContextMenu>
        <ContextMenuTrigger disabled={!devMode} className="min-h-screen">
          <div className="flex flex-col h-screen bg-background overflow-hidden">
            {/* 主体内容 */}
            <div className="flex-1 flex overflow-hidden">
              {/* 侧边栏 */}
              <Sidebar />

              {/* 主要内容区域 */}
              <main className="flex-1 overflow-y-auto p-8">
                <Outlet />
              </main>
            </div>

            {/* 下半部分：日志显示器 */}
            <div className="relative">
              {/* 拖动条 - 只在展开时显示 */}
              {isLogDisplayerExpanded && (
                <div
                  className={cn(
                    "absolute top-0 left-0 right-0 h-2 bg-border hover:bg-primary/50 cursor-ns-resize z-10 transition-colors group",
                    isDragging && "bg-primary"
                  )}
                  onMouseDown={handleMouseDown}
                  title="拖动调节日志面板高度"
                >
                  {/* 拖动指示器 */}
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-0.5 bg-muted-foreground/50 group-hover:bg-primary/70 transition-colors" />
                </div>
              )}

              <div
                className="bg-card border-t shadow-inner"
                style={{
                  height: isLogDisplayerExpanded ? `${logDisplayerHeight}px` : '52px'
                }}
              >
                <LogDisplayer
                  isExpanded={isLogDisplayerExpanded}
                  onToggleExpand={() => setIsLogDisplayerExpanded((prev: boolean) => !prev)}
                />
              </div>
            </div>
          </div>
        </ContextMenuTrigger>
        {devMode && (
          <ContextMenuContent>
            <ContextMenuItem onClick={handleRefresh}>
              <RefreshCwIcon className="mr-2 h-4 w-4" />
              <span>刷新页面</span>
            </ContextMenuItem>
            <ContextMenuSeparator />
            <ContextMenuItem onClick={handleToggleDevTools}>
              <TerminalIcon className="mr-2 h-4 w-4" />
              <span>开发者工具</span>
            </ContextMenuItem>
          </ContextMenuContent>
        )}
      </ContextMenu>
      <Toaster />
      <UpdateInfo />
    </ThemeProvider>
  )
}

export default App
