import { TaskButton, ConnectLiveButton } from '@/components/common/TaskButton'
import { Title } from '@/components/common/Title'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { useAutoVoice, type VoiceItem, determineSpeaker, determineOutputType, dualTrackAudioManager, setupStreamCompletionListener } from '@/hooks/useAutoVoice'
import { Switch } from '@/components/ui/switch'
import { useForbiddenWords } from '@/hooks/useForbiddenWords'
import { useAudioPreview } from '@/hooks/useAudioPreview'
import { useDigitalHumanSettings } from '@/hooks/useDigitalHumanSettings'
import { useCurrentLiveControl } from '@/hooks/useLiveControl'
import { useAIChatStore } from '@/hooks/useAIChat'
import { useAutoReply } from '@/hooks/useAutoReply'
import { useToast } from '@/hooks/useToast'
import { Plus, X, ChevronRight, Volume2, Loader2, CheckCircle, XCircle, Edit, Sparkles, Trash2, Play, Pause, FileAudio, Download, Settings, Video, Monitor, MoreHorizontal, Link, Upload, Eraser, UserCogIcon, RotateCcw } from 'lucide-react'
import { getForbiddenWordsInText } from '@/utils/filter'
import { useState, useRef, useEffect, useMemo } from 'react'
import { useVirtualizer } from '@tanstack/react-virtual'

import { Textarea } from '@/components/ui/textarea'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Progress } from '@/components/ui/progress'
import { speakText } from '@/utils/textToSpeech'
import { generateDigitalHumanVideo } from '@/utils/textToVideo'
import { Label } from '@/components/ui/label'
import { IPC_CHANNELS } from 'shared/ipcChannels'
import { useOBSPlayerSettings } from '@/hooks/useOBSPlayerSettings'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Separator } from '@/components/ui/separator'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'



// 下载音频文件的工具函数
const downloadAudioFile = async (audioUrl: string, filename: string) => {
  try {
    const response = await fetch(audioUrl)
    if (!response.ok) {
      throw new Error(`下载失败: ${response.status} ${response.statusText}`)
    }

    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('下载音频文件失败:', error)
    throw error
  }
}

// 辅助函数：将毫秒转换为 MM:SS 格式
const formatDuration = (ms: number | undefined) => {
  if (ms === undefined || ms === null || isNaN(ms) || ms < 0) return '00:00';
  const totalSeconds = Math.round(ms / 1000);
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

export default function AutoVoice() {
  const {
    isRunning,
    setIsRunning,
    voiceList,
    addVoiceItem,
    addVoiceItemByPriority,
    updateVoiceItem,
    clearVoiceList,
    removeVoiceItem,
    getScriptList,
    createScriptList,
    deleteScriptList,
    renameScriptList,
    switchScriptList,
    getCurrentScriptList,
    getAllScriptLists,
    addScript,
    removeScript,
    clearScriptList,
    generalizationPrompt,
    setGeneralizationPrompt,
    generalizeScript,
    incrementVariantUsage,
    updateScript,
    updateVariant,
    removeVariant,
    exportScripts,
    clearAllVariants,
    clearScriptVariants,
    playbackStatus,

    activeOutputTab,
    setActiveOutputTab,
    // 批量操作状态和方法
    batchGeneralizing,
    batchGeneratingVoices,
    lastGenerationIndex,
    lastGenerationCount,
    lastGenerationTimestamp,
    setBatchGeneralizing,
    setBatchGeneratingVoices,
    setBatchGeneralizingAbortController,
    setBatchGeneratingAbortController,
    stopBatchGeneralizing,
    stopBatchGeneratingVoices,
    saveGenerationProgress,
    clearGenerationProgress,
    hasGenerationProgress
  } = useAutoVoice()

  // 获取当前话术列表
  const scriptList = getScriptList()
  const currentScriptList = getCurrentScriptList()
  const allScriptLists = getAllScriptLists()

  const { forbiddenWords } = useForbiddenWords()
  const { previewStates, togglePreview } = useAudioPreview()
  const { selectedDigitalHumans } = useDigitalHumanSettings()
  const { config } = useAutoReply()

  // 从场控设置中获取直播模式
  const outputMode = config.liveMode

  // 根据当前选中的选项卡过滤语音列表
  const filteredVoiceList = useMemo(() => {
    if (!config.assistantEnabled) {
      // 如果助理功能未开启，所有语音都是主播的
      return voiceList
    }

    return voiceList.filter(voice => {
      // 如果语音项没有speaker信息，根据来源重新确定
      const speaker = voice.speaker || determineSpeaker(voice.source || 'manual', outputMode, config.assistantEnabled, config.assistantMode)

      // // 调试信息
      // console.log('语音项过滤:', {
      //   text: voice.text.substring(0, 20) + '...',
      //   source: voice.source,
      //   originalSpeaker: voice.speaker,
      //   determinedSpeaker: speaker,
      //   activeTab: activeOutputTab,
      //   shouldShow: speaker === activeOutputTab
      // })

      return speaker === activeOutputTab
    })
  }, [voiceList, activeOutputTab, config.assistantEnabled, outputMode])

  // 视频预览状态管理
  const [videoPreviewStates, setVideoPreviewStates] = useState<Record<string, { isPlaying: boolean; videoElement: HTMLVideoElement | null }>>({})

  // 双轨播放状态管理
  const [dualTrackStatus, setDualTrackStatus] = useState({
    host: { isPlaying: false, currentVoiceId: null as string | null },
    assistant: { isPlaying: false, currentVoiceId: null as string | null },
    isDucking: false
  })

  const [importText, setImportText] = useState('')
  const [showImportDialog, setShowImportDialog] = useState(false)
  const [showManualAddDialog, setShowManualAddDialog] = useState(false)
  const [tempManualText, setTempManualText] = useState('')
  // 话术列表管理状态
  const [showCreateListDialog, setShowCreateListDialog] = useState(false)
  const [newListName, setNewListName] = useState('')
  const [showRenameDialog, setShowRenameDialog] = useState(false)
  const [renamingListId, setRenamingListId] = useState('')
  const [renameText, setRenameText] = useState('')

  // 获取持久化的OBS播放器设置
  const { aspectRatio } = useOBSPlayerSettings()

  // 打开OBS播放器独立窗口
  const handleOpenOBSPlayer = async () => {
    try {
      // 传递当前保存的比例设置
      await window.ipcRenderer.invoke(IPC_CHANNELS.obsPlayer.open, aspectRatio)
      toast.success('OBS播放器已打开')
    } catch (error) {
      console.error('打开OBS播放器失败:', error)
      toast.error('打开OBS播放器失败: ' + (error as Error).message)
    }
  }

  // 视频预览功能
  const handleVideoPreview = (voiceId: string, videoUrl: string) => {
    const currentState = videoPreviewStates[voiceId]

    if (currentState?.isPlaying) {
      // 停止预览
      const cleanup = () => {
        // 查找并移除所有相关元素
        const existingOverlay = document.querySelector(`[data-video-preview-overlay="${voiceId}"]`)
        const existingContainer = document.querySelector(`[data-video-preview-container="${voiceId}"]`)

        if (existingOverlay && document.body.contains(existingOverlay)) {
          document.body.removeChild(existingOverlay)
        }
        if (existingContainer && document.body.contains(existingContainer)) {
          document.body.removeChild(existingContainer)
        }

        setVideoPreviewStates(prev => ({
          ...prev,
          [voiceId]: { isPlaying: false, videoElement: null }
        }))
      }
      cleanup()
    } else {
      // 开始预览
      const video = document.createElement('video')
      video.src = videoUrl
      video.style.position = 'fixed'
      video.style.top = '50%'
      video.style.left = '50%'
      video.style.transform = 'translate(-50%, -50%)'
      video.style.zIndex = '9999'
      video.style.maxWidth = '80vw'
      video.style.maxHeight = '80vh'
      video.style.backgroundColor = 'black'
      video.style.border = '2px solid white'
      video.style.borderRadius = '8px'
      video.controls = true
      video.autoplay = true

      // 添加关闭按钮
      const closeButton = document.createElement('button')
      closeButton.innerHTML = '×'
      closeButton.style.position = 'absolute'
      closeButton.style.top = '10px'
      closeButton.style.right = '10px'
      closeButton.style.background = 'rgba(0,0,0,0.7)'
      closeButton.style.color = 'white'
      closeButton.style.border = 'none'
      closeButton.style.borderRadius = '50%'
      closeButton.style.width = '30px'
      closeButton.style.height = '30px'
      closeButton.style.cursor = 'pointer'
      closeButton.style.fontSize = '18px'
      closeButton.style.zIndex = '10000'

      const container = document.createElement('div')
      container.style.position = 'relative'
      container.setAttribute('data-video-preview-container', voiceId)
      container.appendChild(video)
      container.appendChild(closeButton)

      // 点击关闭按钮或视频结束时清理
      const cleanup = () => {
        const existingOverlay = document.querySelector(`[data-video-preview-overlay="${voiceId}"]`)
        const existingContainer = document.querySelector(`[data-video-preview-container="${voiceId}"]`)

        if (existingOverlay && document.body.contains(existingOverlay)) {
          document.body.removeChild(existingOverlay)
        }
        if (existingContainer && document.body.contains(existingContainer)) {
          document.body.removeChild(existingContainer)
        }

        setVideoPreviewStates(prev => ({
          ...prev,
          [voiceId]: { isPlaying: false, videoElement: null }
        }))
      }

      closeButton.onclick = cleanup
      video.onended = cleanup

      // 点击视频外部区域关闭
      const overlay = document.createElement('div')
      overlay.style.position = 'fixed'
      overlay.style.top = '0'
      overlay.style.left = '0'
      overlay.style.width = '100vw'
      overlay.style.height = '100vh'
      overlay.style.backgroundColor = 'rgba(0,0,0,0.5)'
      overlay.style.zIndex = '9998'
      overlay.setAttribute('data-video-preview-overlay', voiceId)
      overlay.onclick = cleanup

      document.body.appendChild(overlay)
      document.body.appendChild(container)

      setVideoPreviewStates(prev => ({
        ...prev,
        [voiceId]: { isPlaying: true, videoElement: video }
      }))
    }
  }

  // 加载状态管理
  const [generalizingScripts, setGeneralizingScripts] = useState<Set<string>>(new Set())
  const [generatingVoices, setGeneratingVoices] = useState<Set<string>>(new Set())
  const [manualGeneratingVoices, setManualGeneratingVoices] = useState<Set<string>>(new Set())

  // 编辑状态管理
  const [showEditScriptDialog, setShowEditScriptDialog] = useState(false)
  const [editingScript, setEditingScript] = useState<{ id: string, text: string } | null>(null)

  // 泛化提示词设置状态管理
  const [showPromptDialog, setShowPromptDialog] = useState(false)
  const [tempPrompt, setTempPrompt] = useState(generalizationPrompt)

  // 变体弹窗状态管理
  const [showVariantsDialog, setShowVariantsDialog] = useState(false)
  const [viewingScript, setViewingScript] = useState<any>(null)

  // 内联编辑状态管理
  const [editingVariantId, setEditingVariantId] = useState<string | null>(null)
  const [editingVariantText, setEditingVariantText] = useState('')

  // 虚拟列表相关
  const parentRef = useRef<HTMLDivElement>(null)
  const voiceListParentRef = useRef<HTMLDivElement>(null)

  // 创建虚拟列表实例
  const virtualizer = useVirtualizer({
    count: scriptList.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 160, // 增加估算高度以包含间距
    overscan: 5, // 预渲染额外的项目数量
    // 启用动态高度测量
    measureElement: (element) => {
      return element?.getBoundingClientRect().height ?? 160
    },
  })

  // 当前显示的语音列表（根据助理功能开启状态决定）
  const currentVoiceList = config.assistantEnabled ? filteredVoiceList : voiceList

  // 语音列表虚拟列表实例
  const voiceVirtualizer = useVirtualizer({
    count: currentVoiceList.length,
    getScrollElement: () => voiceListParentRef.current,
    estimateSize: () => 120, // 估算每个语音项的高度
    overscan: 5, // 预渲染额外的项目数量
    // 启用动态高度测量
    measureElement: (element) => {
      return element?.getBoundingClientRect().height ?? 120
    },
  })

  // 获取AI配置和toast
  const { config: aiConfig, apiKeys, customBaseURL } = useAIChatStore()
  const { toast } = useToast()



  // 移除原有的播放逻辑，现在使用全局播放服务
  // 播放逻辑已移至 useAutoVoice hook 中的全局播放服务

  // 打开变体弹窗
  const handleViewVariants = (script: any) => {
    setViewingScript(script)
    setShowVariantsDialog(true)
    // 重置编辑状态
    setEditingVariantId(null)
    setEditingVariantText('')
  }

  // 开始内联编辑变体
  const handleStartEditVariant = (variantId: string, currentText: string) => {
    setEditingVariantId(variantId)
    setEditingVariantText(currentText)
  }

  // 保存内联编辑的变体
  const handleSaveInlineVariant = (scriptId: string, variantId: string) => {
    if (editingVariantText.trim()) {
      updateVariant(scriptId, variantId, editingVariantText.trim())
      setEditingVariantId(null)
      setEditingVariantText('')
      toast.success('变体已更新')
    }
  }

  // 取消内联编辑
  const handleCancelInlineEdit = () => {
    setEditingVariantId(null)
    setEditingVariantText('')
  }

  // 下载音频文件
  const handleDownloadAudio = async (voice: VoiceItem) => {
    if (!voice.audioUrl) {
      toast.error('音频文件不存在')
      return
    }

    try {
      // 生成文件名：使用语音文本的前20个字符作为文件名
      const textForFilename = voice.text.slice(0, 20).replace(/[^\w\u4e00-\u9fa5]/g, '_')
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
      const filename = `语音_${textForFilename}_${timestamp}.wav`

      await downloadAudioFile(voice.audioUrl, filename)
      toast.success('音频文件下载成功')
    } catch (error) {
      console.error('下载音频文件失败:', error)
      toast.error('下载音频文件失败')
    }
  }

  // 渲染语音/数字人项
  const renderVoiceItem = (index: number) => {
    const voice = currentVoiceList[index];
    const isGenerating = manualGeneratingVoices.has(voice.id);
    const hasAudio = voice.audioUrl && voice.audioUrl.length > 0;
    const hasVideo = voice.videoUrl && voice.videoUrl.length > 0;
    const hasContent = hasAudio || hasVideo;
    const isCurrentlyPlaying = playbackStatus.currentVoiceId === voice.id;

    return (
      <div
        key={voice.id}
        className={`relative p-3 border rounded-md min-h-[4rem] group mb-2 ${isGenerating ? 'bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800' :
          isCurrentlyPlaying ? 'bg-green-50 dark:bg-green-950/20 border-green-200 dark:border-green-800' : ''
          }`}
      >
        <div className="absolute top-1 left-1 bg-muted text-muted-foreground text-xs font-medium px-2 py-1 rounded flex items-center gap-2">
          <span>{index + 1}{hasContent ? ` / ${formatDuration(voice.duration)}` : ''}</span>
          {/* 说话者标识 */}
          {voice.speaker && (
            <span className={`text-xs px-1 py-0.5 rounded ${voice.speaker === 'host'
              ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
              : 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300'
              }`}>
              {voice.speaker === 'host' ? '主播' : '助理'}
            </span>
          )}
          {/* 输出类型标识 */}
          {voice.outputType && (
            <span className="text-xs bg-primary/10 text-primary px-1 py-0.5 rounded">
              {voice.outputType === 'voice' ? '语音' : '数字人'}
            </span>
          )}
          {/* 违禁词检查图标 */}
          {(() => {
            const forbiddenWordsInText = getForbiddenWordsInText(voice.text, forbiddenWords)
            const hasForbiddenWords = forbiddenWordsInText.length > 0
            return (
              <div title={hasForbiddenWords ? `包含违禁词：${forbiddenWordsInText.join('、')}` : "内容安全"}>
                {hasForbiddenWords ? (
                  <XCircle className="h-3 w-3 text-red-500" />
                ) : (
                  <CheckCircle className="h-3 w-3 text-green-500" />
                )}
              </div>
            )
          })()}
        </div>

        <div className="absolute top-1 right-1 flex items-center gap-1">
          {isGenerating && (
            <div className="flex items-center gap-1 text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded mr-1">
              <Loader2 className="h-3 w-3 animate-spin" />
              生成中
            </div>
          )}
          {isCurrentlyPlaying && (
            <div className="flex items-center gap-1 text-xs text-green-600 bg-green-100 px-2 py-1 rounded mr-1">
              <Volume2 className="h-3 w-3" />
              播放中
            </div>
          )}
          {/* 预览播放按钮 */}
          {hasAudio && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => togglePreview(voice.id, voice.audioUrl!)}
              className="opacity-0 group-hover:opacity-100 transition-opacity"
              title={previewStates[voice.id]?.isPlaying ? "停止预览" : "预览音频"}
            >
              {previewStates[voice.id]?.isPlaying ? (
                <Pause className="h-3 w-3" />
              ) : (
                <Play className="h-3 w-3" />
              )}
            </Button>
          )}
          {/* 视频预览按钮 */}
          {hasVideo && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleVideoPreview(voice.id, voice.videoUrl!)}
              className="opacity-0 group-hover:opacity-100 transition-opacity"
              title="预览视频"
            >
              <Video className="h-3 w-3" />
            </Button>
          )}
          {/* 下载按钮 */}
          {hasContent && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                if (hasAudio) {
                  handleDownloadAudio(voice)
                } else if (hasVideo) {
                  // TODO: 实现视频下载功能
                  toast.success('视频下载功能开发中')
                }
              }}
              className="opacity-0 group-hover:opacity-100 transition-opacity"
              title={`下载${hasVideo ? '视频' : '音频'}文件`}
            >
              <Download className="h-3 w-3" />
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={() => removeVoiceItem(voice.id)}
            className="opacity-0 group-hover:opacity-100 transition-opacity"
            title="删除语音"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>

        <div className="mt-6 text-sm">
          {voice.text}
        </div>

        {/* 音色展示 */}
        {voice.voiceFile && (
          <div className="mt-2 flex items-center gap-2 text-xs text-muted-foreground">
            <FileAudio className="h-3 w-3" />
            <span>音色: {voice.voiceFile}</span>
          </div>
        )}

        {/* 数字人形象展示 */}
        {voice.digitalHumanFile && (
          <div className="mt-2 flex items-center gap-2 text-xs text-muted-foreground">
            <Video className="h-3 w-3" />
            <span>数字人: {voice.digitalHumanFile}</span>
          </div>
        )}

        {/* 视频URL展示 */}
        {voice.videoUrl && (
          <div className="mt-2 flex items-center gap-2 text-xs text-muted-foreground">
            <Video className="h-3 w-3" />
            <span className="truncate">视频: {voice.videoUrl}</span>
          </div>
        )}

        {/* 预览播放进度条 */}
        {previewStates[voice.id]?.isPlaying && previewStates[voice.id]?.duration > 0 && (
          <div className="mt-2 space-y-1">
            <div className="flex items-center gap-2 text-xs text-blue-600">
              <Volume2 className="h-3 w-3" />
              <span>预览播放中</span>
            </div>
            <Progress
              value={previewStates[voice.id]?.progress || 0}
              className="h-1"
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>{formatDuration((previewStates[voice.id]?.currentTime || 0) * 1000)}</span>
              <span>{formatDuration((previewStates[voice.id]?.duration || 0) * 1000)}</span>
            </div>
          </div>
        )}

        {/* 全局播放进度条 */}
        {isCurrentlyPlaying && playbackStatus.duration > 0 && (
          <div className="mt-3 space-y-1">
            <div className="flex items-center gap-2 text-xs text-green-600">
              <Volume2 className="h-3 w-3" />
              <span>正在播放</span>
            </div>
            <Progress
              value={playbackStatus.progress}
              className="h-2"
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>{formatDuration(playbackStatus.currentTime * 1000)}</span>
              <span>{formatDuration(playbackStatus.duration * 1000)}</span>
            </div>
          </div>
        )}
      </div>
    );
  }

  // 渲染单个话术项目的函数
  const renderScriptItem = (index: number) => {
    const script = scriptList[index]
    if (!script) return null

    const forbiddenWordsInScript = getForbiddenWordsInText(script.originalText, forbiddenWords)
    const hasForbiddenWords = forbiddenWordsInScript.length > 0

    // 检查是否有运行中的任务
    const hasRunningTask = generatingVoices.has(script.id) || generalizingScripts.has(script.id)

    return (
      <div
        key={script.id}
        className={`border rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 group relative transition-colors mb-2 ${hasForbiddenWords ? 'border-red-200 bg-red-50/30 dark:border-red-800 dark:bg-red-950/10' : ''}`}
      >
        {/* 主要内容区域 */}
        <div className="p-3 relative">
          {/* 左上角悬浮的序号和内容安全图标 */}
          <div className="absolute left-1 top-0 flex items-center gap-1 z-10">
            {/* 序号 */}
            <span className="text-xs text-muted-foreground font-mono bg-white/90 backdrop-blur-sm px-1.5 py-0.5 rounded shadow-sm border">
              {index + 1}
            </span>
            {/* 内容安全图标 */}
            <div
              className="flex items-center justify-center w-5 h-5 bg-white/90 backdrop-blur-sm rounded-full shadow-sm border"
              title={hasForbiddenWords ? `包含违禁词：${forbiddenWordsInScript.join('、')}` : "内容安全"}
            >
              {hasForbiddenWords ? (
                <XCircle className="h-3 w-3 text-red-500" />
              ) : (
                <CheckCircle className="h-3 w-3 text-green-500" />
              )}
            </div>
          </div>

          <div>
            <p className="text-sm font-medium">{script.originalText}</p>
            {script.isGeneralized && (
              <div className="mt-2 flex items-center gap-2">
                <span className="text-xs text-gray-500">
                  已泛化 {script.variants.length} 个变体
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleViewVariants(script)}
                  className="h-6 px-2 text-xs"
                >
                  <ChevronRight className="h-3 w-3 mr-1" />
                  查看变体
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* 底部悬浮操作按钮区域 - 有运行中任务时一直显示，否则鼠标悬停时显示 */}
        <div className={`absolute bottom-2 right-2 flex items-center gap-1 transition-opacity bg-white/90 backdrop-blur-sm rounded-md p-1 shadow-sm border ${hasRunningTask ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'}`}>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleSingleVoiceGeneration(script.id)}
            disabled={generatingVoices.has(script.id)}
            title="转语音"
            className="h-7 px-2"
          >
            {generatingVoices.has(script.id) ? (
              <Loader2 className="h-3 w-3 animate-spin" />
            ) : (
              <Volume2 className="h-3 w-3" />
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleGeneralizeScript(script.id)}
            disabled={generalizingScripts.has(script.id)}
            title="泛化此条"
            className="h-7 px-2"
          >
            {generalizingScripts.has(script.id) ? (
              <Loader2 className="h-3 w-3 animate-spin" />
            ) : (
              <Sparkles className="h-3 w-3" />
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditScript(script.id, script.originalText)}
            title="编辑话术"
            className="h-7 px-2"
          >
            <Edit className="h-3 w-3" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleRemoveScript(index)}
            title="删除话术"
            className="h-7 px-2"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>

      </div>
    )
  }

  // 启动循环生成的函数
  const startBatchGenerateVoices = async () => {
    if (batchGeneratingVoices) {
      return; // 已经在运行中
    }

    if (scriptList.length === 0) {
      return; // 没有话术，不启动
    }

    // 检查是否有上次未完成的生成进度（自动启动时不询问，直接继续）
    let startFromIndex = 0;
    let previousCount = 0;

    if (hasGenerationProgress()) {
      startFromIndex = lastGenerationIndex;
      previousCount = lastGenerationCount;
      console.log(`自动继续从第 ${startFromIndex + 1} 个话术生成`);
    }

    const abortController = new AbortController();
    setBatchGeneratingAbortController(abortController);
    setBatchGeneratingVoices(true);
    let totalSuccessCount = previousCount;
    let currentScriptIndex = startFromIndex; // 用于循环遍历话术列表

    try {
      // 循环生成语音，直到用户停止
      while (!abortController.signal.aborted) {
        // 从话术列表循环选择（而不是随机选择）
        const currentScript = scriptList[currentScriptIndex];
        currentScriptIndex = (currentScriptIndex + 1) % scriptList.length; // 循环到下一个话术

        // 设置当前话术的生成状态
        setGeneratingVoices(prev => new Set(prev).add(currentScript.id));

        try {
          // 获取随机变体文本，如果没有泛化则使用原始文本
          // 这里会从泛化子项中按使用次数优先级随机选择
          const textToSpeak = getRandomVariantText(currentScript);
          console.log('正在循环生成语音:', textToSpeak);

          // 确定说话者和输出类型
          const source = 'script'
          const speaker = determineSpeaker(source, outputMode, config.assistantEnabled, config.assistantMode)
          const outputType = determineOutputType(speaker, outputMode, config.assistantMode)

          // 首先生成语音，传递说话者信息
          const voiceResult = await generateVoiceOnly(textToSpeak, speaker);

          if (voiceResult.audioUrl && !abortController.signal.aborted) {
            const voiceId = Date.now().toString() + Math.random().toString(36).substring(2, 9);
            const newVoice: VoiceItem = {
              id: voiceId,
              text: textToSpeak,
              audioUrl: voiceResult.audioUrl,
              videoUrl: outputType === 'digital-human' ? '' : undefined,
              duration: voiceResult.duration,
              voiceFile: voiceResult.voiceFile,
              digitalHumanFile: outputType === 'digital-human' ? '' : undefined,
              outputType,
              source,
              speaker
            }
            addVoiceItem(newVoice);
            console.log('循环生成语音成功:', textToSpeak);
            totalSuccessCount++;

            // 保存当前生成进度（保存下一个要处理的话术索引）
            saveGenerationProgress(currentScriptIndex, totalSuccessCount);

            // 如果输出类型是数字人，异步生成视频（不阻塞循环）
            if (outputType === 'digital-human') {
              generateDigitalHumanFromVoice(newVoice)
                .then((videoResult) => {
                  updateVoiceItem(voiceId, videoResult);
                  console.log('数字人视频生成成功:', textToSpeak);
                })
                .catch((error) => {
                  console.error('数字人视频生成失败:', textToSpeak, error);
                });
            }
          }
        } catch (error) {
          console.error(`生成话术 "${currentScript.originalText}" 失败:`, error);
        } finally {
          // 清除当前话术的生成状态
          setGeneratingVoices(prev => {
            const newSet = new Set(prev);
            newSet.delete(currentScript.id);
            return newSet;
          });
        }

        // 等待一段时间再生成下一个
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    } catch (error) {
      if (!abortController.signal.aborted) {
        console.error('批量生成过程中出错:', error);
        toast.error(`批量生成过程中出错: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    } finally {
      setBatchGeneratingVoices(false);
      setBatchGeneratingAbortController(null);

      if (!abortController.signal.aborted) {
        // 正常完成，清除进度记录
        clearGenerationProgress();
        if (totalSuccessCount > 0) {
          toast.success(`批量生成完成，共成功生成 ${totalSuccessCount} 个${outputMode === 'voice' ? '语音' : '数字人视频'}`);
        }
      } else {
        // 被中止，保留进度记录以便下次继续
        if (totalSuccessCount > 0) {
          toast.success(`批量生成已停止，共成功生成 ${totalSuccessCount} 个${outputMode === 'voice' ? '语音' : '数字人视频'}。下次开始时可选择继续生成。`);
        }
      }
    }
  };

  const handleAutoVoiceToggle = async () => {
    const newRunningState = !isRunning
    setIsRunning(newRunningState)

    // 如果启动任务且有话术列表，自动启动循环生成
    if (newRunningState && scriptList.length > 0 && !batchGeneratingVoices) {
      // 延迟一点启动循环生成，确保任务状态已更新
      setTimeout(() => {
        startBatchGenerateVoices()
      }, 500)
    }
    // 如果停止任务且正在循环生成，自动停止循环生成
    else if (!newRunningState && batchGeneratingVoices) {
      stopBatchGeneratingVoices()
    }
  }

  // 处理手动添加语音弹窗
  const handleOpenManualAdd = () => {
    setTempManualText('');
    setShowManualAddDialog(true);
  }

  // 生成语音的函数
  const generateVoiceOnly = async (text: string, speaker?: 'host' | 'assistant') => {
    const { audioUrl, duration, voiceFile } = await speakText(text, undefined, speaker);
    return {
      audioUrl,
      duration,
      voiceFile,
      outputType: outputMode
    };
  };

  // 基于已有语音生成数字人视频的函数
  const generateDigitalHumanFromVoice = async (voiceItem: VoiceItem) => {
    if (!voiceItem.audioUrl) {
      throw new Error('语音URL不存在，无法生成数字人视频');
    }

    try {
      // 导入连续数字人视频生成函数
      const { generateContinuousDigitalHumanVideo } = await import('@/utils/continuousDigitalHuman');

      // 使用连续播放功能生成数字人视频
      const result = await generateContinuousDigitalHumanVideo(
        voiceItem.text,
        voiceItem.audioUrl,
        {
          enableContinuous: true,
          speaker: voiceItem.speaker,
          audioDuration: voiceItem.duration ? voiceItem.duration / 1000 : undefined, // 转换为秒
          digitalHumanFile: undefined
        }
      );

      console.log('连续数字人视频生成结果:', {
        isContinuous: result.isContinuous,
        segmentInfo: result.segmentInfo
      });

      return {
        videoUrl: result.videoUrl,
        digitalHumanFile: result.digitalHumanFile,
        isContinuous: result.isContinuous,
        segmentInfo: result.segmentInfo
      };
    } catch (error) {
      console.error('数字人视频生成失败:', error);
      throw error;
    }
  };

  const handleSaveManualAdd = async () => {
    if (!tempManualText.trim()) {
      toast.error('请输入文本内容');
      return;
    }

    const textToAdd = tempManualText.trim();

    // 生成唯一ID用于跟踪这个语音项
    const voiceId = Date.now().toString() + Math.random().toString(36).substring(2, 9);

    // 立即关闭弹窗并清空输入
    setShowManualAddDialog(false);
    setTempManualText('');

    // 立即创建语音项并添加到列表顶部（优先播放）
    const source = 'manual'
    const speaker = determineSpeaker(source, outputMode, config.assistantEnabled, config.assistantMode)
    const outputType = determineOutputType(speaker, outputMode, config.assistantMode)
    const newVoice: VoiceItem = {
      id: voiceId,
      text: textToAdd,
      audioUrl: '', // 先设为空，生成完成后更新
      videoUrl: outputType === 'digital-human' ? '' : undefined,
      duration: 0,
      digitalHumanFile: outputType === 'digital-human' ? '' : undefined,
      outputType,
      source,
      speaker
    }

    // 根据优先级添加到列表
    addVoiceItemByPriority(newVoice);

    // 设置生成状态
    setManualGeneratingVoices(prev => new Set(prev).add(voiceId));

    try {
      console.log('正在将文本转换为语音:', textToAdd);
      // 首先生成语音，传递说话者信息
      const voiceResult = await generateVoiceOnly(textToAdd, speaker);

      if (voiceResult.audioUrl) {
        // 更新语音项的音频信息
        updateVoiceItem(voiceId, voiceResult);
        toast.success('语音生成完成');
        console.log('手动添加语音成功:', textToAdd);

        // 如果输出类型是数字人，继续生成视频
        if (outputType === 'digital-human') {
          try {
            console.log('正在生成数字人视频:', textToAdd);
            // 构造包含音频信息的语音项用于视频生成
            const voiceItemForVideo: VoiceItem = {
              id: voiceId,
              text: textToAdd,
              audioUrl: voiceResult.audioUrl,
              duration: voiceResult.duration,
              voiceFile: voiceResult.voiceFile,
              outputType: outputType
            };
            const videoResult = await generateDigitalHumanFromVoice(voiceItemForVideo);
            updateVoiceItem(voiceId, videoResult);
            toast.success('数字人视频生成完成');
            console.log('数字人视频生成成功:', textToAdd);
          } catch (videoError) {
            console.error('数字人视频生成失败:', videoError);
            toast.error('数字人视频生成失败，但语音已保留');
          }
        }
      } else {
        // 生成失败，移除语音项
        removeVoiceItem(voiceId);
        toast.error('语音生成失败，未返回音频URL');
        console.error('文本转语音成功但未返回音频URL。');
      }
    } catch (error) {
      // 生成失败，移除语音项
      removeVoiceItem(voiceId);
      toast.error('语音生成失败，请重试');
      console.error('添加到列表并转换语音失败:', error);
    } finally {
      // 清除生成状态
      setManualGeneratingVoices(prev => {
        const newSet = new Set(prev);
        newSet.delete(voiceId);
        return newSet;
      });
    }
  }



  // 删除话术函数
  const handleRemoveScript = (index: number) => {
    removeScript(index);
  }

  // 清空话术列表函数
  const handleClearScriptList = () => {
    clearScriptList();
  }

  // 处理导入话术
  const handleImportScripts = () => {
    if (importText.trim()) {
      const scripts = importText.split('\n').filter(line => line.trim() !== '');
      scripts.forEach(script => addScript(script));
      setImportText('');
      setShowImportDialog(false);
    }
  }

  // 处理导出话术
  const handleExportScripts = () => {
    const exportContent = exportScripts();
    if (!exportContent) {
      toast.error('话术列表为空，无法导出');
      return;
    }

    // 创建下载链接
    const blob = new Blob([exportContent], { type: 'text/plain;charset=utf-8' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `话术列表_${new Date().toISOString().slice(0, 10)}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    toast.success('话术列表已导出');
  }

  // 处理清除所有变体
  const handleClearAllVariants = () => {
    clearAllVariants();
    const hasVariants = scriptList.some(script =>
      script.isGeneralized && script.variants.length > 1
    );
    if (hasVariants) {
      toast.success('所有变体已清除，保留原始话术');
    }
  }

  // 话术列表管理处理函数
  const handleCreateScriptList = () => {
    if (newListName.trim()) {
      const newId = createScriptList(newListName.trim())
      setNewListName('')
      setShowCreateListDialog(false)
      toast.success(`话术列表"${newListName.trim()}"已创建`)
    }
  }

  const handleDeleteScriptList = (listId: string, listName: string) => {
    if (allScriptLists.length <= 1) {
      toast.error('至少需要保留一个话术列表')
      return
    }

    if (confirm(`确定要删除话术列表"${listName}"吗？此操作不可撤销。`)) {
      deleteScriptList(listId)
      toast.success(`话术列表"${listName}"已删除`)
    }
  }

  const handleRenameScriptList = () => {
    if (renameText.trim() && renamingListId) {
      renameScriptList(renamingListId, renameText.trim())
      setRenameText('')
      setRenamingListId('')
      setShowRenameDialog(false)
      toast.success('话术列表已重命名')
    }
  }

  const handleSwitchScriptList = (listId: string) => {
    switchScriptList(listId)
    const list = allScriptLists.find(l => l.id === listId)
    if (list) {
      toast.success(`已切换到话术列表"${list.name}"`)
    }
  }

  const openRenameDialog = (listId: string, currentName: string) => {
    setRenamingListId(listId)
    setRenameText(currentName)
    setShowRenameDialog(true)
  }

  // 批量循环生成语音/数字人
  const handleBatchGenerateVoices = async () => {
    if (batchGeneratingVoices) {
      // 如果正在生成，则停止 - 直接停止，不需要二次确认
      stopBatchGeneratingVoices();
      toast.success(`已停止循环生成${outputMode === 'voice' ? '语音' : '数字人视频'}`);
      return;
    }

    if (scriptList.length === 0) {
      toast.error('话术列表为空，请先添加话术。');
      return;
    }

    // 检查是否有上次未完成的生成进度
    let startFromIndex = 0;
    let previousCount = 0;

    if (hasGenerationProgress()) {
      const lastTime = new Date(lastGenerationTimestamp).toLocaleString();
      const confirmed = window.confirm(
        `检测到上次生成进度（已生成 ${lastGenerationCount} 个，时间：${lastTime}）。\n\n是否从第 ${lastGenerationIndex + 1} 个话术继续生成？\n\n点击"确定"继续，点击"取消"从头开始。`
      );

      if (confirmed) {
        startFromIndex = lastGenerationIndex;
        previousCount = lastGenerationCount;
        toast.success(`从第 ${startFromIndex + 1} 个话术继续生成`);
      } else {
        clearGenerationProgress();
        toast.success('从头开始生成');
      }
    }

    const abortController = new AbortController();
    setBatchGeneratingAbortController(abortController);
    setBatchGeneratingVoices(true);
    let totalSuccessCount = previousCount;
    let currentScriptIndex = startFromIndex; // 用于循环遍历话术列表

    try {
      // 循环生成语音，直到用户停止
      while (!abortController.signal.aborted) {
        // 从话术列表循环选择（而不是随机选择）
        const currentScript = scriptList[currentScriptIndex];
        currentScriptIndex = (currentScriptIndex + 1) % scriptList.length; // 循环到下一个话术

        // 设置当前话术的生成状态
        setGeneratingVoices(prev => new Set(prev).add(currentScript.id));

        try {
          // 获取随机变体文本，如果没有泛化则使用原始文本
          // 这里会从泛化子项中按使用次数优先级随机选择
          const textToSpeak = getRandomVariantText(currentScript);
          console.log('正在循环生成语音:', textToSpeak);

          // 确定说话者和输出类型
          const source = 'script'
          const speaker = determineSpeaker(source, outputMode, config.assistantEnabled, config.assistantMode)
          const outputType = determineOutputType(speaker, outputMode, config.assistantMode)

          // 首先生成语音，传递说话者信息
          const voiceResult = await generateVoiceOnly(textToSpeak, speaker);

          if (voiceResult.audioUrl && !abortController.signal.aborted) {
            const voiceId = Date.now().toString() + Math.random().toString(36).substring(2, 9);
            const newVoice: VoiceItem = {
              id: voiceId,
              text: textToSpeak,
              audioUrl: voiceResult.audioUrl,
              videoUrl: outputType === 'digital-human' ? '' : undefined,
              duration: voiceResult.duration,
              voiceFile: voiceResult.voiceFile,
              digitalHumanFile: outputType === 'digital-human' ? '' : undefined,
              outputType,
              source,
              speaker
            }
            addVoiceItem(newVoice);
            console.log('循环生成语音成功:', textToSpeak);
            totalSuccessCount++;

            // 保存当前生成进度（保存下一个要处理的话术索引）
            saveGenerationProgress(currentScriptIndex, totalSuccessCount);

            // 如果输出类型是数字人，异步生成视频（不阻塞循环）
            if (outputType === 'digital-human') {
              generateDigitalHumanFromVoice(newVoice)
                .then((videoResult) => {
                  updateVoiceItem(voiceId, videoResult);
                  console.log('数字人视频生成成功:', textToSpeak);
                })
                .catch((error) => {
                  console.error('数字人视频生成失败:', textToSpeak, error);
                });
            }
          } else if (abortController.signal.aborted) {
            console.log('语音生成被中止:', textToSpeak);
            break;
          } else {
            console.error('循环生成语音失败，未返回音频URL:', textToSpeak);
          }
        } catch (error) {
          if (!abortController.signal.aborted) {
            console.error('循环生成语音失败:', currentScript.originalText, error);
          }
        } finally {
          // 清除当前话术的生成状态
          setGeneratingVoices(prev => {
            const newSet = new Set(prev);
            newSet.delete(currentScript.id);
            return newSet;
          });
        }

        // 检查是否被中止
        if (abortController.signal.aborted) {
          console.log('循环生成语音被中止');
          break;
        }

        // 添加短暂延迟，避免过于频繁的生成
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    } finally {
      setBatchGeneratingVoices(false);
      setBatchGeneratingAbortController(null);

      if (!abortController.signal.aborted) {
        // 正常完成，清除进度记录
        clearGenerationProgress();
        toast.success(`循环生成完成，总共生成 ${totalSuccessCount} 条语音`);
      } else {
        // 被中止，保留进度记录以便下次继续
        toast.success(`循环生成已停止，总共生成 ${totalSuccessCount} 条语音。下次开始时可选择继续生成。`);
      }
    }
  }

  // 获取随机变体文本的辅助函数
  const getRandomVariantText = (script: any): string => {
    if (!script.isGeneralized || script.variants.length === 0) {
      return script.originalText;
    }

    // 找到使用次数最少的变体
    const minUsageCount = Math.min(...script.variants.map((v: any) => v.usageCount));
    const leastUsedVariants = script.variants.filter((v: any) => v.usageCount === minUsageCount);

    // 从使用次数最少的变体中随机选择一个
    const selectedVariant = leastUsedVariants[Math.floor(Math.random() * leastUsedVariants.length)];

    // 增加使用次数
    incrementVariantUsage(script.id, selectedVariant.id);

    return selectedVariant.text;
  }

  // 处理单个话术泛化
  const handleGeneralizeScript = async (scriptId: string) => {
    const script = scriptList.find(s => s.id === scriptId);
    if (!script) return;

    // 设置加载状态
    setGeneralizingScripts(prev => new Set(prev).add(scriptId));

    try {
      // 调用大模型API进行泛化
      const variants = await callLLMForGeneralization(script.originalText);
      if (variants && variants.length > 0) {
        generalizeScript(scriptId, variants);
        toast.success(`成功泛化话术，生成了 ${variants.length} 个变体`);
      } else {
        toast.error('泛化失败，请检查大模型配置');
      }
    } catch (error) {
      console.error('泛化话术失败:', error);
      toast.error('泛化失败，请检查大模型配置');
    } finally {
      // 清除加载状态
      setGeneralizingScripts(prev => {
        const newSet = new Set(prev);
        newSet.delete(scriptId);
        return newSet;
      });
    }
  }

  // 批量泛化所有话术
  const handleBatchGeneralize = async () => {
    if (batchGeneralizing) {
      // 如果正在泛化，则停止 - 直接停止，不需要二次确认
      stopBatchGeneralizing();
      toast.success('已停止批量泛化');
      return;
    }

    if (scriptList.length === 0) {
      toast.error('话术列表为空，请先添加话术。');
      return;
    }

    const unGeneralizedScripts = scriptList.filter(script => !script.isGeneralized);
    if (unGeneralizedScripts.length === 0) {
      toast.success('所有话术都已泛化完成');
      return;
    }

    // // 二次确认
    // const confirmed = window.confirm(`确定要批量泛化 ${unGeneralizedScripts.length} 条话术吗？这可能需要一些时间。`);
    // if (!confirmed) {
    //   return;
    // }

    const abortController = new AbortController();
    setBatchGeneralizingAbortController(abortController);
    setBatchGeneralizing(true);
    let successCount = 0;

    try {
      for (const script of unGeneralizedScripts) {
        // 检查是否被中止
        if (abortController.signal.aborted) {
          console.log('批量泛化被中止');
          break;
        }

        // 设置当前话术的泛化状态
        setGeneralizingScripts(prev => new Set(prev).add(script.id));

        try {
          const variants = await callLLMForGeneralization(script.originalText);
          if (!abortController.signal.aborted && variants && variants.length > 0) {
            generalizeScript(script.id, variants);
            successCount++;
          }
        } catch (error) {
          if (!abortController.signal.aborted) {
            console.error('泛化话术失败:', script.originalText, error);
          }
        } finally {
          // 清除当前话术的泛化状态
          setGeneralizingScripts(prev => {
            const newSet = new Set(prev);
            newSet.delete(script.id);
            return newSet;
          });
        }
      }
    } finally {
      setBatchGeneralizing(false);
      setBatchGeneralizingAbortController(null);

      if (!abortController.signal.aborted) {
        toast.success(`批量泛化完成，成功泛化 ${successCount} 条话术`);
      } else {
        toast.success(`批量泛化已停止，成功泛化 ${successCount} 条语音`);
      }
    }
  }

  // 调用大模型进行泛化
  const callLLMForGeneralization = async (originalText: string): Promise<string[]> => {
    const apiKey = apiKeys[aiConfig.provider];
    if (!apiKey) {
      throw new Error('请先在AI助手页面配置API密钥');
    }

    const prompt = generalizationPrompt.replace('{original_script}', originalText);

    try {
      const messages = [
        {
          role: 'user' as const,
          content: prompt
        }
      ];

      const result = await window.ipcRenderer.invoke(
        IPC_CHANNELS.tasks.aiChat.normalChat,
        {
          messages,
          apiKey,
          provider: aiConfig.provider,
          model: aiConfig.model,
          customBaseURL: aiConfig.provider === 'custom' ? customBaseURL : undefined,
        }
      );

      if (!result) {
        throw new Error('AI服务返回空结果');
      }

      // 解析AI返回的结果，按行分割
      const variants = result.split('\n')
        .map((line: string) => line.trim())
        .filter((line: string) => line.length > 0 && !line.match(/^\d+\./)) // 过滤空行和编号
        .slice(0, 8); // 最多取8个变体

      if (variants.length === 0) {
        throw new Error('AI未能生成有效的变体');
      }

      return variants;
    } catch (error) {
      console.error('调用大模型泛化失败:', error);
      throw error;
    }
  }

  // 泛化提示词设置相关函数
  const handleOpenPromptDialog = () => {
    setTempPrompt(generalizationPrompt)
    setShowPromptDialog(true)
  }

  const handleSavePrompt = () => {
    setGeneralizationPrompt(tempPrompt)
    setShowPromptDialog(false)
    toast.success('AI泛化提示词已保存')
  }

  const handleCancelPrompt = () => {
    setTempPrompt(generalizationPrompt)
    setShowPromptDialog(false)
  }

  // 拼接所有视频
  const handleConcatenateAllVideos = async () => {
    try {
      // 获取所有有视频URL的语音项
      const videoItems = voiceList.filter(item =>
        item.videoUrl &&
        item.videoUrl.trim() !== '' &&
        item.outputType === 'digital-human'
      )

      if (videoItems.length === 0) {
        toast.error('没有可拼接的视频')
        return
      }

      if (videoItems.length === 1) {
        toast.error('至少需要2个视频才能进行拼接')
        return
      }

      toast.success(`开始拼接 ${videoItems.length} 个视频...`)

      // 准备视频URL列表 - 直接传递给后端处理
      const videoUrls = videoItems.map(item => item.videoUrl!)

      if (videoUrls.length === 0) {
        toast.error('没有视频文件可以拼接')
        return
      }

      if (videoUrls.length === 1) {
        toast.error('至少需要2个视频文件才能进行拼接')
        return
      }

      // 生成输出文件名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const outputFilename = `D:\\SD2\\ComfyUI\\output\\concatenated_videos_${timestamp}.mp4`

      // 调用视频拼接IPC
      const result = await (window as any).ipcRenderer.invoke(IPC_CHANNELS.video.concatenateVideos, {
        videoUrls,
        outputFilename
      })

      if (result && result.success) {
        toast.success('视频拼接成功！')

        // 弹出保存对话框
        try {
          const saveResult = await (window as any).ipcRenderer.invoke('dialog:showSaveDialog', {
            title: '保存拼接视频',
            defaultPath: `拼接视频_${new Date().toISOString().replace(/[:.]/g, '-')}.mp4`,
            filters: [
              { name: '视频文件', extensions: ['mp4'] },
              { name: '所有文件', extensions: ['*'] }
            ]
          })

          if (saveResult && !saveResult.canceled && saveResult.filePath) {
            // 复制文件到用户选择的位置
            const copyResult = await (window as any).ipcRenderer.invoke('fs:copyFile', result.outputPath, saveResult.filePath)

            if (copyResult && copyResult.success) {
              toast.success(`视频已保存至: ${saveResult.filePath}`)

              // 可选：打开文件所在目录
              try {
                await (window as any).ipcRenderer.invoke('shell:showItemInFolder', saveResult.filePath)
              } catch (error) {
                console.warn('无法打开文件目录:', error)
              }
            } else {
              toast.error('保存文件失败')
            }
          }
        } catch (error) {
          console.error('保存对话框错误:', error)
          toast.error('保存对话框打开失败')
        }
      } else {
        toast.error(`视频拼接失败: ${result?.error || '未知错误'}`)
      }

    } catch (error) {
      console.error('拼接视频时发生错误:', error)
      toast.error('拼接视频时发生错误')
    }
  }

  // 单独转语音/数字人
  const handleSingleVoiceGeneration = async (scriptId: string) => {
    const script = scriptList.find(s => s.id === scriptId);
    if (!script) return;

    // 设置加载状态
    setGeneratingVoices(prev => new Set(prev).add(scriptId));

    try {
      // 获取要使用的文本（智能选择变体）
      const textToSpeak = getRandomVariantText(script);

      // 确定说话者和输出类型
      const source = 'script'
      const speaker = determineSpeaker(source, outputMode, config.assistantEnabled, config.assistantMode)
      const outputType = determineOutputType(speaker, outputMode, config.assistantMode)

      // 首先生成语音，传递说话者信息
      const voiceResult = await generateVoiceOnly(textToSpeak, speaker);

      if (voiceResult.audioUrl) {
        const voiceId = Date.now().toString() + Math.random().toString(36).substring(2, 9);
        const newVoice: VoiceItem = {
          id: voiceId,
          text: textToSpeak,
          audioUrl: voiceResult.audioUrl,
          videoUrl: outputType === 'digital-human' ? '' : undefined,
          duration: voiceResult.duration,
          voiceFile: voiceResult.voiceFile,
          digitalHumanFile: outputType === 'digital-human' ? '' : undefined,
          outputType,
          source,
          speaker
        };

        addVoiceItemByPriority(newVoice);
        toast.success('语音已添加到播放列表');

        // 如果输出类型是数字人，继续生成视频
        if (outputType === 'digital-human') {
          try {
            const videoResult = await generateDigitalHumanFromVoice(newVoice);
            updateVoiceItem(voiceId, videoResult);
            toast.success('数字人视频生成完成');
          } catch (videoError) {
            console.error('数字人视频生成失败:', videoError);
            toast.error('数字人视频生成失败，但语音已保留');
          }
        }
      } else {
        toast.error('语音生成失败，请检查服务器');
      }
    } catch (error) {
      console.error('单独转语音失败:', error);
      toast.error('语音生成失败，请检查服务器');
    } finally {
      // 清除加载状态
      setGeneratingVoices(prev => {
        const newSet = new Set(prev);
        newSet.delete(scriptId);
        return newSet;
      });
    }
  }

  // 编辑处理函数
  const handleEditScript = (scriptId: string, currentText: string) => {
    setEditingScript({ id: scriptId, text: currentText })
    setShowEditScriptDialog(true)
  }

  const handleSaveScript = () => {
    if (editingScript) {
      updateScript(editingScript.id, editingScript.text)
      setEditingScript(null)
      setShowEditScriptDialog(false)
      toast.success('话术已更新')
    }
  }

  const handleCancelEditScript = () => {
    setEditingScript(null)
    setShowEditScriptDialog(false)
  }



  const handleRemoveVariant = (scriptId: string, variantId: string) => {
    removeVariant(scriptId, variantId)

    // 如果正在编辑被删除的变体，取消编辑状态
    if (editingVariantId === variantId) {
      setEditingVariantId(null)
      setEditingVariantText('')
    }

    toast.success('变体已删除')
  }

  // 处理清空单个话术的变体
  const handleClearScriptVariants = (scriptId: string) => {
    const script = scriptList.find(s => s.id === scriptId)
    if (!script || !script.isGeneralized || script.variants.length <= 1) {
      toast.error('该话术没有变体可以清空')
      return
    }

    if (confirm('确定要清空该话术的所有变体吗？此操作将保留原始话术，但删除所有泛化生成的变体，且不可撤销。')) {
      clearScriptVariants(scriptId)

      // 如果正在编辑被清空的变体，取消编辑状态
      if (editingVariantId) {
        setEditingVariantId(null)
        setEditingVariantText('')
      }

      toast.success('变体已清空，保留原始话术')
    }
  }

  const platform = useCurrentLiveControl(context => context.platform)
  const isTaskForbidden = platform !== 'douyin' && platform !== 'buyin' && platform !== 'wxchannel'

  // 设置推流完成事件监听器
  useEffect(() => {
    setupStreamCompletionListener()
  }, [])

  // 自动同步 viewingScript 与 scriptList 的变化
  useEffect(() => {
    if (viewingScript && showVariantsDialog) {
      const updatedScript = scriptList.find(s => s.id === viewingScript.id)
      if (updatedScript && JSON.stringify(updatedScript) !== JSON.stringify(viewingScript)) {
        setViewingScript({ ...updatedScript })
      }
    }
  }, [scriptList, viewingScript, showVariantsDialog])

  // 监控双轨播放状态
  useEffect(() => {
    const updateDualTrackStatus = () => {
      const status = dualTrackAudioManager.getPlaybackStatus()
      setDualTrackStatus({
        host: {
          isPlaying: status.host.isRunning && !!status.host.currentVoiceId,
          currentVoiceId: status.host.currentVoiceId
        },
        assistant: {
          isPlaying: status.assistant.isRunning && !!status.assistant.currentVoiceId,
          currentVoiceId: status.assistant.currentVoiceId
        },
        isDucking: status.isDucking
      })
    }

    // 初始更新
    updateDualTrackStatus()

    // 定期更新状态
    const interval = setInterval(updateDualTrackStatus, 500)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="container space-y-4">
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <Title
              title="直播话术"
              description={`根据话术列表自动生成${outputMode === 'voice' ? '语音' : '数字人视频'}播放`}
            />
          </div>
          <div className="flex items-center gap-4">
            {/* 连接直播间按钮 - 只在未连接时显示，左侧小一些 */}
            <ConnectLiveButton />
            <TaskButton
              isTaskRunning={isRunning}
              onStartStop={handleAutoVoiceToggle}
              forbidden={isTaskForbidden}
            />
          </div>
        </div>
      </div>

      {/* 话术和语音列表区域 */}
      <div className="grid grid-cols-2 gap-4">
        {/* 左侧：话术列表 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2 w-full">
                {/* 话术列表选择器 */}
                <Select value={currentScriptList?.id || ''} onValueChange={handleSwitchScriptList}>
                  <SelectTrigger className="w-[200px] h-8">
                    <SelectValue placeholder="选择话术列表" />
                  </SelectTrigger>
                  <SelectContent>
                    {allScriptLists.map((list) => (
                      <SelectItem key={list.id} value={list.id}>
                        <div className="flex items-center justify-between w-full">
                          <span>{list.name}</span>
                          <span className="text-xs text-muted-foreground ml-2">
                            ({list.scripts.length})
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowCreateListDialog(true)}
                  className="h-8 px-2"
                  title="创建新话术列表"
                >
                  <Plus className="h-3 w-3" />
                </Button>

                {/* 生成进度提示 */}
                {hasGenerationProgress() && (
                  <span className="text-xs text-orange-600 ml-auto">
                    下次将从第 {lastGenerationIndex + 1} 个开始（已生成 {lastGenerationCount} 个）
                  </span>
                )}
              </div>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-56 p-0" align="end">
                  <div className="p-1">
                    <button
                      className="flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground"
                      onClick={() => setShowImportDialog(true)}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      导入话术
                    </button>
                    <button
                      className="flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground"
                      onClick={handleExportScripts}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      导出话术
                    </button>
                    <button
                      className="flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground"
                      onClick={handleOpenPromptDialog}
                    >
                      <UserCogIcon className="h-4 w-4 mr-2" />
                      提示词
                    </button>
                    <Separator className="my-1" />
                    {/* 话术列表管理选项 */}
                    <button
                      className="flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground"
                      onClick={() => setShowCreateListDialog(true)}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      新建话术
                    </button>
                    {currentScriptList && (
                      <button
                        className="flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground"
                        onClick={() => openRenameDialog(currentScriptList.id, currentScriptList.name)}
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        重命名话术
                      </button>
                    )}
                    {allScriptLists.length > 1 && currentScriptList && (
                      <button
                        className="flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground text-destructive"
                        onClick={() => handleDeleteScriptList(currentScriptList.id, currentScriptList.name)}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        删除话术
                      </button>
                    )}
                    {scriptList.length > 0 && (
                      <>
                        <Separator className="my-1" />
                        <button
                          className="flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground"
                          onClick={handleBatchGeneralize}
                        >
                          {batchGeneralizing ? (
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          ) : (
                            <Sparkles className="h-4 w-4 mr-2" />
                          )}
                          {batchGeneralizing ? "停止泛化" : "泛化全部"}
                        </button>
                        <button
                          className="flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground"
                          onClick={handleBatchGenerateVoices}
                        >
                          {batchGeneratingVoices ? (
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          ) : (
                            <Volume2 className="h-4 w-4 mr-2" />
                          )}
                          {batchGeneratingVoices ? "停止生成" : "开始生成"}
                        </button>
                        <Separator className="my-1" />
                        <button
                          className="flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground"
                          onClick={handleClearAllVariants}
                        >
                          <Eraser className="h-4 w-4 mr-2" />
                          清除变体
                        </button>
                        {hasGenerationProgress() && (
                          <button
                            className="flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground text-orange-600"
                            onClick={() => {
                              clearGenerationProgress();
                              toast.success('已清除生成进度记录');
                            }}
                          >
                            <RotateCcw className="h-4 w-4 mr-2" />
                            清除进度记录
                          </button>
                        )}
                        <button
                          className="flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground text-destructive"
                          onClick={handleClearScriptList}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          清空话术列表
                        </button>
                      </>
                    )}
                  </div>
                </PopoverContent>
              </Popover>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {scriptList.length === 0 ? (
              <div className="text-center text-muted-foreground py-8">
                <p>暂无话术</p>
                <p className="text-sm mt-2">点击"导入话术"按钮添加话术内容</p>
              </div>
            ) : (
              <div
                ref={parentRef}
                className="h-[calc(100vh-320px)] overflow-auto pr-4"
              >
                <div
                  style={{
                    height: `${virtualizer.getTotalSize()}px`,
                    width: '100%',
                    position: 'relative',
                  }}
                >
                  {virtualizer.getVirtualItems().map((virtualItem) => (
                    <div
                      key={virtualItem.key}
                      data-index={virtualItem.index}
                      ref={virtualizer.measureElement}
                      style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        transform: `translateY(${virtualItem.start}px)`,
                      }}
                    >
                      {renderScriptItem(virtualItem.index)}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 右侧：语音或数字人输出列表 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <span>输出队列{/* -{outputMode === 'voice' ? '语音' : '数字人'} */}</span>
                {config.assistantEnabled && (
                  <div className="flex items-center gap-2 text-sm">
                    <div className="flex items-center gap-1">
                      <span className={`w-2 h-2 rounded-full ${dualTrackStatus.host.isPlaying ? 'bg-blue-500 animate-pulse' : 'bg-gray-300'}`}></span>
                      <span className="text-xs text-muted-foreground">主播</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <span className={`w-2 h-2 rounded-full ${dualTrackStatus.assistant.isPlaying ? 'bg-green-500 animate-pulse' : 'bg-gray-300'}`}></span>
                      <span className="text-xs text-muted-foreground">助理</span>
                    </div>
                    {dualTrackStatus.isDucking && (
                      <div className="flex items-center gap-1 text-orange-500" title="主播音频正在闪避">
                        <Volume2 className="h-3 w-3" />
                        <span className="text-xs">闪避</span>
                      </div>
                    )}
                  </div>
                )}
              </div>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-56 p-0" align="end">
                  <div className="p-1">
                    <button
                      className="flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground"
                      onClick={handleOpenManualAdd}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      插入语音
                    </button>

                    {/* 数字人播放器窗口控制 */}
                    {outputMode === 'digital-human' && (
                      <>
                        <Separator className="my-1" />
                        <button
                          className="flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground"
                          onClick={async () => {
                            try {
                              const { simpleDigitalHumanPlaybackService } = await import('@/services/SimpleDigitalHumanPlaybackService')
                              await simpleDigitalHumanPlaybackService.openHostPlayer()
                              toast.success('主播播放器窗口已打开')
                            } catch (error) {
                              console.error('打开主播播放器失败:', error)
                              toast.error('打开主播播放器失败')
                            }
                          }}
                        >
                          <Monitor className="h-4 w-4 mr-2" />
                          主播播放器
                        </button>
                        {config.assistantEnabled && (
                          <button
                            className="flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground"
                            onClick={async () => {
                              try {
                                const { simpleDigitalHumanPlaybackService } = await import('@/services/SimpleDigitalHumanPlaybackService')
                                await simpleDigitalHumanPlaybackService.openAssistantPlayer()
                                toast.success('助理播放器窗口已打开')
                              } catch (error) {
                                console.error('打开助理播放器失败:', error)
                                toast.error('打开助理播放器失败')
                              }
                            }}
                          >
                            <Monitor className="h-4 w-4 mr-2" />
                            助理播放器
                          </button>
                        )}

                        {/* 拼接所有视频按钮 */}
                        {voiceList.filter(item => item.videoUrl && item.outputType === 'digital-human').length > 1 && (
                          <>
                            <Separator className="my-1" />
                            <button
                              className="flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground"
                              onClick={handleConcatenateAllVideos}
                            >
                              <Link className="h-4 w-4 mr-2" />
                              拼接输出视频
                            </button>
                          </>
                        )}
                      </>
                    )}

                    {voiceList.length > 0 && (
                      <>
                        <Separator className="my-1" />
                        <button
                          className="flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground text-destructive"
                          onClick={clearVoiceList}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          清空列表
                        </button>
                      </>
                    )}
                  </div>
                </PopoverContent>
              </Popover>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {config.assistantEnabled ? (
              <Tabs value={activeOutputTab} onValueChange={(value) => setActiveOutputTab(value as 'host' | 'assistant')}>
                <TabsList className="grid w-full grid-cols-2 mb-4">
                  <TabsTrigger value="host" className="flex items-center gap-2">
                    <div className="flex items-center gap-2">
                      <span className={`w-2 h-2 rounded-full ${dualTrackStatus.host.isPlaying ? 'bg-blue-500 animate-pulse' : 'bg-gray-400'}`}></span>
                      {dualTrackStatus.isDucking && <Volume2 className="h-3 w-3 text-orange-500" />}
                      主播输出 ({voiceList.filter(v => (v.speaker || determineSpeaker(v.source || 'manual', outputMode, config.assistantEnabled, config.assistantMode)) === 'host').length})
                    </div>
                  </TabsTrigger>
                  <TabsTrigger value="assistant" className="flex items-center gap-2">
                    <div className="flex items-center gap-2">
                      <span className={`w-2 h-2 rounded-full ${dualTrackStatus.assistant.isPlaying ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`}></span>
                      {config.assistantMode === 'voice' ? '语音助理' : '数字人助理'} ({voiceList.filter(v => (v.speaker || determineSpeaker(v.source || 'manual', outputMode, config.assistantEnabled, config.assistantMode)) === 'assistant').length})
                    </div>
                  </TabsTrigger>
                </TabsList>
                <TabsContent value={activeOutputTab} className="mt-0">
                  {filteredVoiceList.length === 0 ? (
                    <div className="text-center text-muted-foreground py-8 h-[calc(100vh-420px)] flex flex-col items-center justify-center">
                      <p>暂无{activeOutputTab === 'host' ? '主播' : '助理'}语音</p>
                      <p className="text-sm mt-2">
                        {activeOutputTab === 'host'
                          ? '从左侧话术列表添加或手动输入文本'
                          : '自动回复、被动互动等功能会生成助理语音'}
                      </p>
                    </div>
                  ) : (
                    <div
                      ref={voiceListParentRef}
                      className="h-[calc(100vh-420px)] overflow-auto pr-4"
                    >
                      <div
                        style={{
                          height: `${voiceVirtualizer.getTotalSize()}px`,
                          width: '100%',
                          position: 'relative',
                        }}
                      >
                        {voiceVirtualizer.getVirtualItems().map((virtualItem) => (
                          <div
                            key={virtualItem.key}
                            data-index={virtualItem.index}
                            ref={voiceVirtualizer.measureElement}
                            style={{
                              position: 'absolute',
                              top: 0,
                              left: 0,
                              width: '100%',
                              transform: `translateY(${virtualItem.start}px)`,
                            }}
                          >
                            {renderVoiceItem(virtualItem.index)}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            ) : (
              <div className="space-y-4">
                {/* 助理功能未开启时，显示所有语音 */}
                {currentVoiceList.length === 0 ? (
                  <div className="text-center text-muted-foreground py-8 h-[calc(100vh-370px)] flex flex-col items-center justify-center">
                    <p>暂无语音</p>
                    <p className="text-sm mt-2">从左侧话术列表添加或手动输入文本</p>
                  </div>
                ) : (
                  <div
                    ref={voiceListParentRef}
                    className="h-[calc(100vh-370px)] overflow-auto pr-4"
                  >
                    <div
                      style={{
                        height: `${voiceVirtualizer.getTotalSize()}px`,
                        width: '100%',
                        position: 'relative',
                      }}
                    >
                      {voiceVirtualizer.getVirtualItems().map((virtualItem) => (
                        <div
                          key={virtualItem.key}
                          data-index={virtualItem.index}
                          ref={voiceVirtualizer.measureElement}
                          style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '100%',
                            transform: `translateY(${virtualItem.start}px)`,
                          }}
                        >
                          {renderVoiceItem(virtualItem.index)}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card >
      </div >

      {/* 导入话术对话框 */}
      {
        showImportDialog && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <Card className="w-[600px] max-h-[80vh]">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>导入话术</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowImportDialog(false)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="import-textarea" className="text-sm font-medium">
                    话术内容（每行一条话术）
                  </Label>
                  <Textarea
                    id="import-textarea"
                    placeholder="请输入话术内容，每行一条话术，例如：&#10;咱们现在拍下的话，下午都是可以发走。&#10;感谢下单的朋友，感谢支持，感谢关注。&#10;咱们这个是一号链接，第一个选项。"
                    value={importText}
                    onChange={e => setImportText(e.target.value)}
                    rows={10}
                    className="mt-2"
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setShowImportDialog(false)}
                  >
                    取消
                  </Button>
                  <Button onClick={handleImportScripts}>
                    导入话术
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )
      }



      {/* 手动添加语音对话框 */}
      {
        showManualAddDialog && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <Card className="w-[500px] max-h-[80vh] overflow-hidden">
              <CardHeader>
                <CardTitle>插入语音</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="manual-text-input">语音文本内容</Label>
                  <Textarea
                    id="manual-text-input"
                    placeholder="输入要转换为语音的文本..."
                    value={tempManualText}
                    onChange={e => setTempManualText(e.target.value)}
                    rows={4}
                    className="mt-2"
                  />
                  <p className="text-sm text-gray-500 mb-2">
                    将在顶部插入此语音，优先播放。
                  </p>
                </div>
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setShowManualAddDialog(false)}
                  >
                    取消
                  </Button>
                  <Button
                    onClick={handleSaveManualAdd}
                    disabled={!tempManualText.trim()}
                  >
                    生成{outputMode === 'voice' ? '语音' : '数字人视频'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )
      }

      {/* 编辑话术对话框 */}
      {
        showEditScriptDialog && editingScript && (() => {
          // 检查编辑中的话术内容是否包含违禁词
          const forbiddenWordsInEditScript = getForbiddenWordsInText(editingScript.text, forbiddenWords)
          const hasEditScriptForbiddenWords = forbiddenWordsInEditScript.length > 0

          return (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <Card className="w-[600px] max-h-[80vh] overflow-hidden">
                <CardHeader>
                  <CardTitle>编辑话术</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="edit-script-textarea">话术内容</Label>
                    <p className="text-sm text-gray-500 mb-2">
                      编辑话术内容，可以去掉违禁词或优化表达
                    </p>
                    <div className="relative">
                      <Textarea
                        id="edit-script-textarea"
                        placeholder="输入话术内容..."
                        value={editingScript.text}
                        onChange={e => setEditingScript({ ...editingScript, text: e.target.value })}
                        rows={Math.max(3, Math.ceil(editingScript.text.length / 20))}
                        className={`mt-2 pr-10 ${hasEditScriptForbiddenWords ? 'border-red-300 bg-red-50/50 dark:border-red-700 dark:bg-red-950/20' : ''}`}
                        autoFocus
                      />
                      {/* 内容安全检查图标 */}
                      <div
                        className="absolute top-4 right-3 flex items-center justify-center"
                        title={hasEditScriptForbiddenWords ? `包含违禁词：${forbiddenWordsInEditScript.join('、')}` : "内容安全"}
                      >
                        {hasEditScriptForbiddenWords ? (
                          <XCircle className="h-4 w-4 text-red-500" />
                        ) : (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        )}
                      </div>
                    </div>
                    {hasEditScriptForbiddenWords && (
                      <p className="text-sm text-red-600 dark:text-red-400 mt-1">
                        检测到违禁词：{forbiddenWordsInEditScript.join('、')}
                      </p>
                    )}
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      onClick={handleCancelEditScript}
                    >
                      取消
                    </Button>
                    <Button
                      onClick={handleSaveScript}
                      disabled={!editingScript.text.trim()}
                    >
                      保存
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          )
        })()
      }



      {/* AI泛化提示词设置对话框 */}
      <Dialog open={showPromptDialog} onOpenChange={setShowPromptDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>AI泛化提示词设置（直播话术）</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <Textarea
              placeholder="输入完整的AI泛化提示词..."
              value={tempPrompt}
              onChange={e => setTempPrompt(e.target.value)}
              className="text-sm min-h-[300px]"
            />
            <p className="text-xs text-muted-foreground">
              完整的AI泛化提示词，使用 <code>{'{original_script}'}</code> 作为原始话术的占位符。此设置将应用于所有话术的AI泛化。
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={handleCancelPrompt}>
              取消
            </Button>
            <Button onClick={handleSavePrompt}>
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 变体查看弹窗 */}
      <Dialog open={showVariantsDialog} onOpenChange={setShowVariantsDialog}>
        <DialogContent className="w-[70vw] max-w-[70vw] sm:max-w-[70vw] max-h-[80vh]">
          <DialogHeader>
            <div className="flex items-center justify-between">
              <DialogTitle>话术变体列表（使用次数低的优先使用）</DialogTitle>
              {viewingScript && viewingScript.isGeneralized && viewingScript.variants.length > 1 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleClearScriptVariants(viewingScript.id)}
                  className="text-destructive hover:text-destructive"
                >
                  <Eraser className="h-4 w-4 mr-2" />
                  清空变体
                </Button>
              )}
            </div>
          </DialogHeader>
          {viewingScript && (
            <div className="space-y-4">
              {/* 原始话术 */}
              <div className="p-3 bg-muted rounded-md">
                <div className="text-sm font-medium text-muted-foreground mb-1">原始话术：</div>
                <div className="text-sm">{viewingScript.originalText}</div>
              </div>

              {/* 变体列表 */}
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">
                  变体列表 ({viewingScript.variants.length} 个)：
                </div>
                <ScrollArea className="h-[400px] pr-4">
                  <div className="space-y-2">
                    {viewingScript.variants.map((variant: any, variantIndex: number) => {
                      const forbiddenWordsInVariant = getForbiddenWordsInText(
                        editingVariantId === variant.id ? editingVariantText : variant.text,
                        forbiddenWords
                      )
                      const hasVariantForbiddenWords = forbiddenWordsInVariant.length > 0
                      const isEditing = editingVariantId === variant.id

                      return (
                        <div
                          key={variant.id}
                          className={`text-sm p-3 bg-background hover:bg-muted/50 rounded border transition-colors ${hasVariantForbiddenWords ? 'border-red-200 bg-red-50/50 dark:border-red-800 dark:bg-red-950/20' : ''} ${isEditing ? 'ring-2 ring-blue-500' : ''}`}
                        >
                          {/* 顶部信息栏 */}
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-3">
                              {/* 序号 */}
                              <div className="bg-blue-100 dark:bg-blue-950 text-blue-600 dark:text-blue-400 font-medium w-5 h-5 rounded-full flex items-center justify-center border border-blue-200 dark:border-blue-800 text-xs">
                                {variantIndex + 1}
                              </div>

                              {/* 已播次数 */}
                              <div className="flex items-center gap-1 text-muted-foreground text-xs">
                                <span>已播</span>
                                <span className="font-medium text-foreground">{variant.usageCount}</span>
                                <span>次</span>
                              </div>

                              {/* 内容安全图标 */}
                              <div
                                className="flex items-center justify-center"
                                title={hasVariantForbiddenWords ? `包含违禁词：${forbiddenWordsInVariant.join('、')}` : "内容安全"}
                              >
                                {hasVariantForbiddenWords ? (
                                  <XCircle className="h-4 w-4 text-red-500 dark:text-red-400" />
                                ) : (
                                  <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400" />
                                )}
                              </div>
                            </div>

                            {/* 操作按钮 */}
                            <div className="flex items-center gap-1">
                              {isEditing ? (
                                <>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleSaveInlineVariant(viewingScript.id, variant.id)}
                                    className="h-6 w-6 p-0 text-green-600 hover:text-green-700"
                                    title="保存"
                                    disabled={!editingVariantText.trim()}
                                  >
                                    <CheckCircle className="h-3.5 w-3.5" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={handleCancelInlineEdit}
                                    className="h-6 w-6 p-0 text-gray-600 hover:text-gray-700"
                                    title="取消"
                                  >
                                    <X className="h-3.5 w-3.5" />
                                  </Button>
                                </>
                              ) : (
                                <>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleStartEditVariant(variant.id, variant.text)}
                                    className="h-6 w-6 p-0"
                                    title="编辑变体"
                                  >
                                    <Edit className="h-3.5 w-3.5" />
                                  </Button>
                                  {viewingScript.variants.length > 1 && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleRemoveVariant(viewingScript.id, variant.id)}
                                      className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                                      title="删除变体"
                                    >
                                      <X className="h-3.5 w-3.5" />
                                    </Button>
                                  )}
                                </>
                              )}
                            </div>
                          </div>

                          {/* 变体内容或编辑框 */}
                          <div className="text-foreground">
                            {isEditing ? (
                              <Textarea
                                value={editingVariantText}
                                onChange={(e) => setEditingVariantText(e.target.value)}
                                className={`text-sm resize-none ${hasVariantForbiddenWords ? 'border-red-300 bg-red-50/50 dark:border-red-700 dark:bg-red-950/20' : ''}`}
                                rows={Math.max(2, Math.ceil(editingVariantText.length / 40))}
                                autoFocus
                              />
                            ) : (
                              <div>{variant.text}</div>
                            )}
                          </div>


                        </div>
                      )
                    })}
                  </div>
                </ScrollArea>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* 创建话术列表对话框 */}
      <Dialog open={showCreateListDialog} onOpenChange={setShowCreateListDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>创建新话术列表</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="new-list-name">话术列表名称</Label>
              <input
                id="new-list-name"
                type="text"
                placeholder="请输入话术列表名称"
                value={newListName}
                onChange={(e) => setNewListName(e.target.value)}
                className="w-full mt-2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                autoFocus
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleCreateScriptList()
                  }
                }}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateListDialog(false)}>
              取消
            </Button>
            <Button onClick={handleCreateScriptList} disabled={!newListName.trim()}>
              创建
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 重命名话术列表对话框 */}
      <Dialog open={showRenameDialog} onOpenChange={setShowRenameDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>重命名话术列表</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="rename-list-name">话术列表名称</Label>
              <input
                id="rename-list-name"
                type="text"
                placeholder="请输入新的话术列表名称"
                value={renameText}
                onChange={(e) => setRenameText(e.target.value)}
                className="w-full mt-2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                autoFocus
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleRenameScriptList()
                  }
                }}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRenameDialog(false)}>
              取消
            </Button>
            <Button onClick={handleRenameScriptList} disabled={!renameText.trim()}>
              重命名
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

    </div>
  )
}